# DigitalOcean Deployment Guide

## Required Environment Variables

### Core Configuration (Required)
Add these through **DigitalOcean App Platform Dashboard** → **Settings** → **App-Level Environment Variables**:

| Variable | Value | Type | Description |
|----------|-------|------|-------------|
| `PORT` | *Auto-set* | N/A | **Don't add - DigitalOcean sets this automatically** |
| `HOSTNAME` | `0.0.0.0` | Regular | Accept external connections |
| `NODE_ENV` | `production` | Regular | Production environment (already in app.yaml) |
| `MONGO_DB_URI` | `mongodb://...` | **Secret** | Your MongoDB connection string |
| `ROOT_URL` | `https://your-app.ondigitalocean.app` | Regular | Your app's URL |

### Performance Tuning (Recommended for 4GB instances)
| Variable | Value | Type | Description |
|----------|-------|------|-------------|
| `MEM_SOFT_MB` | `2500` | Regular | Memory warning threshold |
| `MEM_HARD_MB` | `3500` | Regular | Memory critical threshold |
| `MEM_TREND_MB` | `500` | Regular | Memory trend alert threshold |

### Scaling (Optional - when ready)
| Variable | Value | Type | Description |
|----------|-------|------|-------------|
| `VALKEY_URL` | `redis://...` | **Secret** | Valkey/Redis for multi-instance |

### API Keys & Secrets (Add as needed)
| Variable | Type | Description |
|----------|------|-------------|
| `UCAN_SK` | **Secret** | Authentication secret |
| `SENDGRID_API_KEY` | **Secret** | Email service |
| `STRIPE_TEST_SK` | **Secret** | Payment processing |
| `TWILIO_API_KEY` | **Secret** | SMS service |
| `OAUTH_GOOGLE_KEY` | **Secret** | Google OAuth |
| `OAUTH_GOOGLE_SECRET` | **Secret** | Google OAuth |

## Deployment Steps

### 1. Set Environment Variables
1. Go to your app in DigitalOcean dashboard
2. Click **Settings** → **App-Level Environment Variables**
3. Add the required variables above
4. Mark sensitive values as **"Secret"**

### 2. Deploy
```bash
git add .
git commit -m "Configure for DigitalOcean deployment"
git push origin main
```

### 3. Monitor Deployment
- Watch **Runtime Logs** for startup messages
- Check **Activity** tab for deployment status
- Verify health checks pass

## Health Check Endpoints

- **Liveness**: `GET /healthz` → Returns `200 "ok"`
- **Readiness**: `GET /readyz` → Returns `200 "ready"` or `503 "db-unavailable"`

## Expected Startup Logs

```bash
✅ [Socket.IO] Configuring Redis/Valkey adapter...
✅ [Socket.IO] Redis adapter configured successfully
✅ [watchdog] Started
✅ [ResourceMonitor] Started
✅ Feathers app listening on http://0.0.0.0:8080
```

## Troubleshooting

### Deployment Fails with "connection refused"
- **Don't set `PORT`** - DigitalOcean sets this automatically
- Check `HOSTNAME` is set to `0.0.0.0`
- Verify your app listens on `process.env.PORT` (should be automatic)

### Health checks fail
- Check `MONGO_DB_URI` is correct
- Check MongoDB is accessible
- Check logs for database connection errors

### Memory warnings
- Adjust `MEM_SOFT_MB` and `MEM_HARD_MB` for your instance size
- Monitor actual memory usage in logs

### Socket.IO issues with multiple instances
- Add `VALKEY_URL` for Redis/Valkey database
- Ensure Valkey database is created and connected

## Scaling Configuration

Current setup: **2-8 instances** of **4GB RAM** each
- Automatically scales based on CPU usage (70% threshold)
- Minimum 2 instances for crash resilience
- Maximum 8 instances for high load

## Cost Estimate

- **2 instances minimum**: ~$10/month
- **Valkey database**: ~$15/month (when added)
- **Total**: ~$25/month base cost
