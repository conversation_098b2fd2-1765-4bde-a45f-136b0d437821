import {HookContext} from "../../declarations.js";

import {relateOto, removeOto, relateMtm, removeMtm, relateOtm, removeOtm} from './relaters.js';
// const {relateOto, removeOto, relateMtm, removeMtm, relateOtm, removeOtm} = require('./relate-hooks');
import { RelateParams } from "./relaters.js";

// const {GeneralError} = require('@feathersjs/errors');
type CallType = 'remove'|'relate';
type Methods = 'oto'|'otm'|'mtm';

const actions = (type:CallType, path:Methods) => {
  const paths = {
    remove: {
      oto: removeOto,
      otm: removeOtm,
      mtm: removeMtm,
    },
    relate: {
      oto: relateOto,
      otm: relateOtm,
      mtm: relateMtm,
    },
  };
  return paths[type][path];
};

export const relate = (method:string, config:RelateParams, err = ''):(c:HookContext) => Promise<HookContext> => {
  return (context:HookContext):Promise<HookContext> => {
    let type = context.method === 'remove' || context.data?.deleted ? 'remove' : 'relate';

    return actions(type as CallType, method.toLowerCase() as Methods)({...config})(context);
  };
};

