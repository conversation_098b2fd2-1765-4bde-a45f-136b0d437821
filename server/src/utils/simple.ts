import {ObjectId} from 'mongodb';
import {HookContext} from '../declarations.js';

export const fakeId = '123456781234567812345678';

export const dollarString = (val: string | number, symbol = '$', dec?: number, def = 'N/A'): string => {
    const v = typeof val !== 'number' ? parseFloat(val) : val;
    if (isNaN(v)) return def;
    const decimal = dec || dec === 0 ? dec : 2;
    const valDec = v.toFixed(decimal);
    return (symbol) + valDec.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

export const maybeObjectId = (val: any) => {
    if (typeof val === 'string') return ObjectId.createFromHexString(val);
    return val;
}

export const idConvert = (path: string) => {
    return async (context: HookContext) => {
        const v = (context.params.query || {})[path]
        if (v) {
            if (typeof v === 'string') {
                context.params.query[path] = ObjectId.createFromHexString(v)
                return context;
            }
            if (v.$in) {
                context.params.query[path].$in = v.$in.map((a: any) => maybeObjectId(a))
            }
            if (v.$nin) {
                context.params.query[path].$nin = v.$nin.map((a: any) => maybeObjectId(a))
            }
            if (v.$eq) {
                context.params.query[path] = maybeObjectId(v.$eq);
            }
            if (v.$ne) {
                context.params.query[path] = { $ne: maybeObjectId(v.$ne) };
            }
        }
        return context;
    }
}

export const errHook = (noThrow?: boolean) => {
    return (context: HookContext) => {
        console.log(`Error on ${context.path} - ${context.method}: ${context.error.message}`);
        if (noThrow) return context;
        else throw new Error(context.error.data[0].message)
    }
}
