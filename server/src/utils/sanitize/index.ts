import sanitizeHtml from 'sanitize-html';
import {HookContext} from '../../declarations.js';
import {_get, _set} from '../dash-utils.js';

const allowedTags = ['div', 'span', 'code', 'br', 'p', 'b', 'a', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'h7', 'i'];

const defaultOptions = {
    allowedTags
}

type Options = {
    allowedTags?: string[],
    nonBooleanAttributes?: string[],
    disallowedTagsMode?: string,
    allowedAttributes?: string[],
    selfClosing?: string[],
    allowedSchemes?: string[],
    allowedSchemesByTag?: object,
    allowedSchemesAppliedToAttributes?: string[],
    allowProtocolRelative?: boolean,
    enforceHtmlBoundary?: boolean,
    parseStyleAttributes?: boolean
}
export const sanitize = (text: string, options?: Options) => {
    return sanitizeHtml(text, {...defaultOptions, ...options})
}

export const plainTextSanitize = (text:string) => {
        return text.replace(/<\/?[^>]+(>|$)/g, "");
}

// To get nested fields separate with '.' to re-map a subObject use '.*'
export const scrub = (fields: Array<string>): (c: HookContext) => HookContext => {
    return (context: HookContext): HookContext => {

        const scrubField = (field: string, data: any): void => {
            const v = _get(data, field);
            if (typeof v === 'string') return _set(data, field, sanitize(v));
            else return data;
        }

        const loopField = (field: string, data: any): any => {
            const f = field.split('.');
            const idx = f.indexOf('*');
            if (idx > -1) {
                const subObj = _get(data, f.slice(0, idx + 1)) || {}
                if (typeof subObj === 'object') {
                    for (const subField in subObj) {
                        data = loopField(`${subField}.${f.slice(idx).join('.')}`, data)
                    }
                }
                return data;
            } else {
                return scrubField(field, data);
            }
        }

        if (Array.isArray(context.data)) context.data = context.data.map(a => {
            for (const field of fields) {
                a = loopField(field, a)
            }
            return a;
        })
        else {
            for (const field of fields) {
                context.data = loopField(field, context.data);
            }
        }
        return context;
    }
}
