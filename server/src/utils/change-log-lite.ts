import {HookContext} from '../declarations.js';

// Lightweight version of logChange without feathers-ucan dependencies
// For services that just need basic change logging
export const logChange = () => {
    return async (context: HookContext) => {
        // Basic logging without the heavy feathers-ucan functionality
        if (context.method === 'create' || context.method === 'patch' || context.method === 'update' || context.method === 'remove') {
            console.log(`Change logged: ${context.path}.${context.method} by user ${context.params?.user?.id || 'unknown'}`);
        }
        return context;
    }
}
