// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    passkeysDataValidator,
    passkeysPatchValidator,
    passkeysQueryValidator,
    passkeysResolver,
    passkeysExternalResolver,
    passkeysDataResolver,
    passkeysPatchResolver,
    passkeysQueryResolver
} from './passkeys.schema.js'

import type {Application} from '../../declarations.js'
import {PasskeysService, getOptions} from './passkeys.class.js'
import {passkeysPath, passkeysMethods} from './passkeys.shared.js'
import {logChange} from '../../../utils/change-log.js';

export * from './passkeys.class.js'
export * from './passkeys.schema.js'

// hooks/passkeys.hooks.ts


// Only allow displayName from external patch; strip security-critical fields on external calls
const restrictExternalPatch = async (context) => {
    if (!context.params.provider) return context; // internal calls (server) ok
    if (context.method !== 'patch' && context.method !== 'update') return context;

    const allowed = new Set(['displayName']);
    const stripFields = (obj) => {
        if (!obj) return obj;
        for (const k of Object.keys(obj)) {
            if (!allowed.has(k)) delete obj[k];
        }
        return obj;
    };

    if (Array.isArray(context.data)) {
        context.data = context.data.map(stripFields);
    } else {
        context.data = stripFields(context.data);
    }
    return context;
};

// Hide publicKey from external responses by default
const stripPublicKeyExternal = async (context) => {
    if (!context.params.provider) return context;
    const strip = (item) => {
        if (!item) return item;
        delete item.publicKey;
        return item;
    };
    if (Array.isArray(context.result?.data)) {
        context.result.data = context.result.data.map(strip);
    } else {
        context.result = strip(context.result);
    }
    return context;
};

// A configure function that registers the service and its hooks via `app.configure`
export const passkeys = (app: Application) => {
    // Register our service on the Feathers application
    app.use(passkeysPath, new PasskeysService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: passkeysMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(passkeysPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(passkeysExternalResolver),
                schemaHooks.resolveResult(passkeysResolver)
            ]
        },
        before: {
            all: [
                schemaHooks.validateQuery(passkeysQueryValidator),
                schemaHooks.resolveQuery(passkeysQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                logChange(),
                schemaHooks.validateData(passkeysDataValidator),
                schemaHooks.resolveData(passkeysDataResolver)
            ],
            patch: [
                logChange(),
                schemaHooks.validateData(passkeysPatchValidator),
                schemaHooks.resolveData(passkeysPatchResolver),
                restrictExternalPatch
            ],
            remove: [logChange()]
        },
        after: {
            all: [stripPublicKeyExternal]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [passkeysPath]: PasskeysService
    }
}
