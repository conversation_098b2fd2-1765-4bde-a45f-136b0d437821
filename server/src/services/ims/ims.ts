// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    imsDataValidator,
    imsPatchValidator,
    imsQueryValidator,
    imsResolver,
    imsExternalResolver,
    imsDataResolver,
    imsPatchResolver,
    imsQueryResolver
} from './ims.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {ImsService, getOptions} from './ims.class.js'
import {imsPath, imsMethods} from './ims.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, noThrow, setExists} from 'feathers-ucan';
import {getJoin} from '../../utils/fast-join.js';
import {logChange} from '../../utils/change-log.js';
import {relate} from '../../utils/relate/index.js';
import {addSessionFp} from '../../utils/request-safety.js';
import {sanitize} from '../../utils/sanitize/index.js';
import {NextFunction} from '@feathersjs/feathers';

export * from './ims.class.js'
export * from './ims.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['ims', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['ims', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        create: noThrow,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    const cap_subjects: any = []

    if (context.params.special_change) ucanArgs.patch = noThrow
    else if (context.method === 'patch') {
        const existing = await loadExists(context, {params: {runJoin: {ims_person: true}}});
        context = setExists(context, existing);
        if (!existing?._fastjoin?.person?.login) {
            ucanArgs.patch = noThrow
        } else {
            if (existing.team) {
                cap_subjects.push(existing.team)
                ucanArgs.patch.unshift([`teams:${existing.team}`, 'teamAdmin'])
            }
            if (existing.host) {
                cap_subjects.push(existing.host)
                ucanArgs.patch.unshift([`hosts:${existing.host}`, 'hostAdmin'])
                ucanArgs.patch.unshift([`hosts:${existing.host}`, 'teamAdmin'])
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*',
        loginPass: [[['person/owner'], '*'], [['support*login/_id'], '*']],
        cap_subjects
    })(context)
}


const runJoins = async (context: HookContext) => {
    if (context.params.runJoin?.ims_person) return getJoin({service: 'ppls', herePath: 'person'})(context)
    return context;
}

const relatePerson = async (context: HookContext) => {
    return await relate('otm', {
        herePath: 'person',
        therePath: 'ims',
        thereService: 'ppls',
        paramsName: 'ims_person_relate'
    })(context)
}

const addMessage = async (context: HookContext, next: NextFunction) => {
    if (context.method === 'patch' && context.data.new_message) {
        const msg = {
            ...context.data.new_message,
            body: sanitize(context.data.new_message.body),
            sentAt: new Date()
        }
        context.data.$push = {messages: {$each: [msg], $position: 0}}
        delete context.data.new_message
    }
    await next();
}

const scrubInitialMessage = (context: HookContext) => {
    if (context.data.messages) {
        context.data.messages = context.data.messages.map(a => {
            return {
                ...a,
                body: sanitize(a.body)
            }
        })
    }
    return context
}

const getTop = (msg: any) => {
    return async (context: HookContext) => {
        let support;
        const {host, plan} = msg;
        if (!host) {
            const hosts = await new CoreCall('hosts', context).find({
                runJoin: {w_support: {planId: plan}},
                query: {appDefault: true},
                $limit: 1
            })
                .catch(err => {
                    console.error(`Could not get default host on create message: ${err.message}.`)
                    return {data: []}
                })
            support = hosts.data[0];
        } else support = await new CoreCall('hosts', context).get(host, {runJoin: {w_support: {planId: plan}}})
            .catch(err => {
                console.error(`Could not get host on create message: ${err.message}.`)
                return {host: undefined}
            })
        if (support) {
            context.params.support_host = support
            const team = support._fastjoin?.team
            if (team) {
                let top = (team.online || [])[0] || (team.priorities || [])[0] || (team.refs || [])[0]
                let idx = Infinity
                for (let i = 0; i < (team.online || []).length; i++) {
                    const newIdx = (team.priorities || []).indexOf(team.online[i])
                    if (newIdx > -1 && newIdx < idx) {
                        idx = newIdx
                        top = team.online[i];
                    }
                    if (newIdx === 0) break;
                }
                if (top) {
                    const ref = await new CoreCall('refs', context).get(top, {runJoin: {ref_person: true}})
                        .catch(err => {
                            console.log(`Could not get top ref: ${err.mesage}`)
                        })

                    return {ref, host: support}
                }
            } else return { host:support }
        }
        return {host: undefined}
    }
}
const checkSupport = async (context: HookContext) => {
    const setTop = async () => {
        const {ref: top, host} = await getTop(context.data)(context)
        if (!top) context.data.orphan = true;
        else {
            context.data.orphan = false;
            context.data.support = {
                [`${top._id}`]: {
                    login: top._fastjoin?.person?.login,
                    name: top.name,
                    email: top.email,
                    phone: top.phone?.number?.e164,
                    status: 1,
                    sendTo: top.sendTo || 'phone'
                }
            }
        }
        if (host && !context.data.host) context.data.host = host._id
    }
    if (context.method === 'create' && context.data.messages) {
        await setTop()
    } else if (context.data.$push?.messages) {
        const ex = await loadExists(context);
        context = await setExists(context, ex);
        const on = Object.keys(ex.support || {}).filter(a => !ex.support[a].offline)
        if (!on.length) {
            await setTop()
        }
    }
    return context;
}

import tClient from 'twilio';
import sgMail from '@sendgrid/mail';

const forwardMessage = async (context: HookContext) => {
    if (!context.result.orphan && !context.params.skip_hooks) {
        const activeIds = Object.keys(context.result.support || {}).filter(a => !context.result.support[a].offline);

        const apiSends = {
            'phone': async (pn: string, ending: string) => {
                const {key, id, from} = context.app.get('sms') || {key: null};
                const client = tClient(id, key)
                return await client.messages.create({
                    to: pn,
                    body: `CommonCare support chat from ${context.result.participant?.name || context.result._id}\n${context.result.messages[0].body?.substring(0, 120)}${ending ? '\n\n' + ending : ''}`,
                    from: from || '+19842380013'
                })
            },
            'email': async (email: string, ending: string) => {
                const {key} = context.app.get('mailer') || {key: null};
                sgMail.setApiKey(key as any);
                return await sgMail.send({
                    from: 'CommonCare Admin <<EMAIL>>',
                    to: [email],
                    subject: `Chat id ${context.result._id} with ${context.result.participant?.name || 'unknown'}`,
                    text: ending + '\n\n' + context.result.messages[0].body
                })
            }
        }
        const errs: Array<any> = []
        const sendToSource = async (k: string, src: string) => {
            const contact = context.result.support[k][src];
            const endMap = {'email': 'email', 'phone': 'text'}
            const ending = context.result.participant?.sendTo !== 'in-app' ? `Respond via ${endMap[context.result.participant.sendTo]} @ ${context.result.participant[context.result.participant.sendTo]}` : ''
            return await apiSends[src](contact, ending)
                .catch(err => {
                    console.error(`Error sending to source: ${src}. Contact: ${contact}. Err: ${err.message}`)
                    errs.push({code: err.code, message: err.message, pid: k})
                })
        }
        const promises: any = []
        for (let i = 0; i < activeIds.length; i++) {
            const source = context.result.support[activeIds[i]].sendTo || 'phone'
            if (source !== 'in-app') {
                promises.push(sendToSource(activeIds[i], source))
            }
        }
        await Promise.all(promises)
        if (errs.length) {
            context.result.messages[0].errs = errs
            if (context.result._id) {
                new CoreCall('ims', context)._patch(context.result._id, {$set: {[`messages.0.errs`]: errs}}, {
                    skip_hooks: true,
                    admin_pass: true
                })
                    .catch(err => console.error(`Could not patch errors to message: ${err.message}`))
            } else {
                console.error('Cannot patch message errors: missing _id in context.result')
            }
        }
    } else if (context.params.support_host) {
        const email = (context.params.support_host.emails || [])[0]
        if (email) {
            const {key} = context.app.get('mailer') || {key: null};
            sgMail.setApiKey(key as any);
            await sgMail.send({
                from: 'CommonCare Admin <<EMAIL>>',
                to: [email],
                subject: `Unattended CommonCare chat id ${context.result._id}`,
                text: 'You got an unattended chat in your CommonCare account. Login to respond ASAP'
            })
                .catch(err => console.error(`Could not send orphan support notification to host ${context.params.support_host._id}: ${err.message}`))
        }
    }
    return context;
}

//TODO: build twilio matching integration for these
const matchAnon = async (context: HookContext): Promise<HookContext> => {
    if (context.data.texting || context.data.calling) {
        const path = context.data.texting ? 'texting' : 'calling';
        const ex = await new CoreCall('ims', context).find({
            query: {
                [path]: true,
                ['participant.fp']: context.data.participant.fp,
                $limit: 1
            }
        })
        if (ex?.total) context.result = ex.data[0];
    }
    return context;
}

/** Search for all people that a given (host) has ims with - uses the remainder of the query passed and adds a pipeline to search matching people
 *
 * Returns a list of ims with the _fastjoin.person attached and _fastjoin.history of all older ims for that same person
 *
 * */


const peopleSearch = async (context: HookContext) => {
    if (context.params.runJoin?.ims_people) {
        const ims = await new CoreCall('ims', context).find({
            query: {
                ...context.params.query,
                person: { $exists: true },
                $limit: Math.max(context.params.query.$limit || 100, 100),
            },
            pipeline: [
                { $lookup: {
                        from: 'ppls',
                        localField: 'person',
                        foreignField: '_id',
                        as: 'person'
                    }
                },
                { $unwind: '$person' },
                // Optional: group by person to prevent duplicates
                {
                    $group: {
                        _id: '$person._id',
                        person: { $first: '$person' },
                        firstMessage: { $first: '$$ROOT' },
                        history: { $push: '$$ROOT' }
                    }
                },
                {
                    $project: {
                        _id: 0,
                        person: 1,
                        'firstMessage._fastjoin': {
                            person: '$person',
                            history: {
                                $slice: ['$history', 1, { $size: '$history' }]
                            }
                        },
                        firstMessage: 1
                    }
                },
                {
                    $replaceRoot: { newRoot: '$firstMessage' }
                }
            ]
        })
        context.result = ims;
        // const byPerson = new Map<string, any[]>();
        //
        // for (const msg of ims.data) {
        //     const pid = String(msg.person);
        //     if (!byPerson.has(pid)) byPerson.set(pid, []);
        //     byPerson.get(pid)!.push(msg);
        // }
        // if (byPerson.size === 0) {
        //     context.result = [];
        //     return context;
        // }
        //
        // const pplIds = Array.from(byPerson.keys());
        //
        // // 2. Query all matching people in one shot
        // const ppl = await new CoreCall('ppls', context).find({
        //     query: {
        //         _id: { $in: pplIds },
        //         $limit: pplIds.length
        //     }
        // });
        // const pplById = new Map<string, any>();
        // for (const p of ppl.data) {
        //     pplById.set(String(p._id), p);
        // }
        //
        // const finalList:any = [];
        // for (const [personId, imsArray] of byPerson.entries()) {
        //     const [first, ...rest] = imsArray;
        //     first._fastjoin = {
        //         person: pplById.get(personId),
        //         history: rest
        //     };
        //     finalList.push(first);
        // }
        //
        // context.result = finalList;
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const ims = (app: Application) => {
    // Register our service on the Feathers application
    app.use(imsPath, new ImsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: imsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(imsPath).hooks({
        around: {
            all: [
                addMessage,
                schemaHooks.resolveExternal(imsExternalResolver),
                schemaHooks.resolveResult(imsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(imsQueryValidator),
                schemaHooks.resolveQuery(imsQueryResolver)
            ],
            find: [peopleSearch],
            get: [],
            create: [
                matchAnon,
                scrubInitialMessage,
                schemaHooks.validateData(imsDataValidator),
                schemaHooks.resolveData(imsDataResolver),
                relatePerson,
                addSessionFp,
                checkSupport
            ],
            patch: [
                schemaHooks.validateData(imsPatchValidator),
                schemaHooks.resolveData(imsPatchResolver),
                relatePerson,
                addSessionFp,
                checkSupport
            ],
            remove: [relatePerson]
        },
        after: {
            all: [runJoins],
            create: [
                relatePerson,
                forwardMessage
            ],
            patch: [
                relatePerson,
                forwardMessage
            ],
            remove: [relatePerson]
        },
        error: {
            all: [
                ctx => {
                    console.log('ims error', ctx.error.message)
                }
            ]
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [imsPath]: ImsService
    }
}
