// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    calendarsDataValidator,
    calendarsPatchValidator,
    calendarsQueryValidator,
    calendarsResolver,
    calendarsExternalResolver,
    calendarsDataResolver,
    calendarsPatchResolver,
    calendarsQueryResolver
} from './calendars.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {CalendarsService, getOptions} from './calendars.class.js'
import {calendarsPath, calendarsMethods} from './calendars.shared.js'
import {getJoin} from '../../../utils/fast-join.js';
import {logChange} from '../../../utils/change-log.js';
import {allUcanAuth, anyAuth, CapabilityParts, loadExists, setExists} from 'feathers-ucan';

const authenticate = async (context: HookContext) => {
    const writer = [['refs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['refs', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const loginPass: any = [];
    const cap_subjects:any = []

    if (['patch', 'remove'].includes(context.method)) {
        const ex = await loadExists(context, {params: {runJoin: {calendar_owner: true}}});
        context = setExists(context, ex);
        if (ex.ownerService === 'refs') {
            const ref = ex._fastjoin.owner
            if (ref) {
                loginPass.push([['_fastjoin.owner.person/owner'], '*'])
                const hostNamespace = `hosts:${ref.host}`
                cap_subjects.push(ref.host)
                const hostWrite: CapabilityParts[] = [[hostNamespace, 'hostAdmin'], [hostNamespace, 'refAdmin']]
                for (const w of hostWrite) {
                    ucanArgs.patch.unshift(w);
                }
                if (ref.org) {
                    const orgNamespace = `orgs:${ref.org}`
                    cap_subjects.push(ref.org)
                    const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']];
                    for (const w of orgWrite) {
                        ucanArgs.patch.unshift(w);
                    }
                }
            }
        } else if (ex.ownerService === 'teams') {
            const team = ex._fastjoin.owner;
            const teamNamespace = `teams:${ex.owner}`
            cap_subjects.push(ex.owner)
            const teamWrite: CapabilityParts[] = [[teamNamespace, 'teamAdmin']]
            for (const w of teamWrite) {
                ucanArgs.patch.unshift(w);
            }
            if (team) {
                if (team.host) {
                    const hostNamespace = `hosts:${team.host}`
                    cap_subjects.push(team.host);
                    const hostWrite: CapabilityParts[] = [[hostNamespace, 'hostAdmin'], [hostNamespace, 'teamAdmin']]
                    for (const w of hostWrite) {
                        ucanArgs.patch.unshift(w);
                    }
                }
            }
        } else if (ex.ownerService === 'hosts') {
            const hostNamespace = `hosts:${ex.owner}`
            cap_subjects.push(ex.owner);
            const hostWrite: CapabilityParts[] = [[hostNamespace, 'hostAdmin']]
            for (const w of hostWrite) {
                ucanArgs.patch.unshift(w);
            }
        }
    }

    return await allUcanAuth<HookContext>(ucanArgs, {
        creatorPass: ['*'],
        adminPass: ['patch', 'create', 'remove'],
        loginPass,
        cap_subjects
    })(context) as any;
}

const runJoins = async (context: HookContext) => {
    if (context.params.runJoin?.calendar_owner) {
        return getJoin({
            service: context.result.ownerService,
            herePath: 'owner'
        })(context)
            .catch(err => {
                console.log(`Error relating calendar owner for service ${context.result.ownerService} - id: ${context.result._id}. Err message: ${err.message}`)
                return context;
            })
    }
    return context;
}


export * from './calendars.class.js'
export * from './calendars.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const calendars = (app: Application) => {
    // Register our service on the Feathers application
    app.use(calendarsPath, new CalendarsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: calendarsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(calendarsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(calendarsExternalResolver),
                schemaHooks.resolveResult(calendarsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(calendarsQueryValidator),
                schemaHooks.resolveQuery(calendarsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(calendarsDataValidator),
                schemaHooks.resolveData(calendarsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(calendarsPatchValidator),
                schemaHooks.resolveData(calendarsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: [
                runJoins
            ]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [calendarsPath]: CalendarsService
    }
}
