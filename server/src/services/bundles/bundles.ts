// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    bundlesDataValidator,
    bundlesPatchValidator,
    bundlesQueryValidator,
    bundlesResolver,
    bundlesExternalResolver,
    bundlesDataResolver,
    bundlesPatchResolver,
    bundlesQueryResolver
} from './bundles.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {BundlesService, getOptions} from './bundles.class.js'
import {bundlesPath, bundlesMethods} from './bundles.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {findJoin, getJoin} from '../../../utils/fast-join.js';
import {logChange, logHistory} from '../../../utils/change-log.js';
import {relate} from '../../../utils/relate/index.js';

export * from './bundles.class.js'
export * from './bundles.schema.js'

const relateProvider = async (context: HookContext): Promise<HookContext> => {
    return relate('otm', {
        herePath: 'provider',
        therePath: 'bundles',
        thereService: 'providers',
        paramsName: 'providerBundle'
    })(context)
}

import { addCreator } from '../cats/cats.js';

const authenticate = async (context: HookContext) => {
    const writer = [['providers', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['providers', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []

    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {

            const existing = await loadExists(context);
            context = setExists(context, existing);

            if (existing) {
                const orgNamespace = `orgs:${existing.org || context.data.org}`;
                cap_subjects.push(existing.org || context.data.org)
                const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'providerAdmin']]
                const providerNamespace = `providers:${existing.provider}`;
                cap_subjects.push(existing.provider)
                const providerWrite: CapabilityParts[] = [[providerNamespace, 'WRITE'], [providerNamespace, 'providerAdmin']]
                for (const w of orgWrite) {
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                    ucanArgs.remove.unshift(w);
                }
                for (const w of providerWrite) {
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                    ucanArgs.remove.unshift(w);
                }
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['managers/owner'], '*']],
        cap_subjects
    })(context) as any;
}

const getProviderOrg = async (context: HookContext): Promise<HookContext> => {
    if (context.data.provider) {
        const provider = await new CoreCall('providers', context).get(context.data.provider as any)
        context.data.org = provider.org
    }
    return context;
}

const updateTotal = async (context: HookContext): Promise<HookContext> => {
    if(context.params.update_bundle_price && !context.params.addPrices){
        const ex = await loadExists(context);
        context = setExists(context, ex);
        const prices = await new CoreCall('prices', context)._find({
            skip_hooks: true, admin_pass: true,
            query: {
                bundle: context.id,
                $limit: (ex.bundles || []).length + 5
            },
            pipeline: [
                {
                    $group: {
                        _id: '$bundle',
                        price_total: { $sum: '$price' }
                    }
                }
            ]
        })

        console.log('got prices for bundle price', prices);

        const total = prices.data[0]?.price_total || 0;
        console.log('now total', total);
        context.data.price = total;

    }
    return context;
}


const runJoins = async (context: HookContext): Promise<HookContext> => {
    const { pb_provider } = context.params.runJoin || {};
    if(pb_provider) return getJoin({ service: 'providers', herePath: 'provider' })(context);
    return context;
}

const reportChanges = async (context: HookContext): Promise<HookContext> => {
    const allData = { ...context.data, ...context.data.$set, ...context.data.$addToSet, ...context.data.$pull };
    if(allData.price || allData.procedures){
        const ex = await loadExists(context);
        context = setExists(context, ex);
        const newPrice = context.data.price || context.data.$set?.price;
        let run = false;
        if(newPrice !== ex.price) run = true;
        else if(allData.procedures) run = true;
        if(run){
            await new CoreCall('networks', context, { skipJoins: true }).patch(null, { $addToSet: { bundle_changes: context.id} }, { query: { bundles: { $in: [context.id] }}})
                .catch(err => {
                    console.error(`Failed to report changes for bundle to networks. Bundle _id ${context.id} - err: ${err.message}`)
                })
        }
    }
    return context;
}

import multer from 'multer';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    req.feathers.file = req.file;
    req.feathers.core = req.query.core
    return next();
}
import { priceUploads } from './utils/add-prices.js';

// A configure function that registers the service and its hooks via `app.configure`
export const bundles = (app: Application) => {
    // Register our service on the Feathers application
    app.use(bundlesPath,
        restMiddleware,
        new BundlesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: bundlesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(bundlesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(bundlesExternalResolver),
                schemaHooks.resolveResult(bundlesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(bundlesQueryValidator),
                schemaHooks.resolveQuery(bundlesQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                addCreator,
                schemaHooks.validateData(bundlesDataValidator),
                schemaHooks.resolveData(bundlesDataResolver),
                getProviderOrg,
                relateProvider
            ],
            patch: [
                priceUploads,
                updateTotal,
                schemaHooks.validateData(bundlesPatchValidator),
                schemaHooks.resolveData(bundlesPatchResolver),
                relateProvider,
                logHistory(['price', 'plans', 'networks', 'discounts', 'procedures']),
                reportChanges
            ],
            remove: [relateProvider]
        },
        after: {
            all: [runJoins],
            create: [relateProvider],
            patch: [priceUploads, relateProvider],
            remove: [relateProvider]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [bundlesPath]: any
    }
}
