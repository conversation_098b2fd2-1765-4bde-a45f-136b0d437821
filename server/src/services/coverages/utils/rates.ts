import {HookContext} from '../../../declarations.js';
import {createParse} from '../../uploads/parser/index.js';
import {CoreCall, loadExists, setExists} from 'feathers-ucan';

import {_set} from 'symbol-ucan';
import {AnyObj} from '../../../../utils/types.js';

// passing a spreadsheet with columns for age and rate
export const rateUpload = async (context: HookContext): Promise<HookContext> => {
    const {addCoverage: {rate = undefined, headers = {}} = {rate: undefined, headers: {}}} = context.params
    if (rate && context.params.file) {
        if (context.type === 'before') {
            context.params.query = {$limit: 1000}
            const {result} = createParse(context);

            const errs: { row: number, data: any, key: string, err: string, throw?: boolean }[] = [];

            const sheet = rate.sheet ? result.filter(a => a.name === rate.sheet)[0] : result[0];
            const sliceCount = rate.omitFirstRow ? 1 : 0;
            const rateByAge = {};
            const errors = {};

            const existing = await loadExists(context);
            context = setExists(context, existing);

            sheet.data.slice(sliceCount).map((a, i) => {
                const age = a[headers.age] || -1;
                if (!(age > -1 && age < 121)) errors[i] = {data: a, throw: false, message: 'Invalid age'};
                else if (!a[headers.rate] || typeof Number(a[headers.rate]) !== 'number' && !errors[i]) {
                    errors[i] = {data: a, message: 'Invalid rate'}
                } else {
                    rateByAge[age] = a[headers.rate]
                }
            })

            context.result = await new CoreCall('coverages', context, {skipJoins: true}).patch(context.id as any, {'premiums.rateByAge': rateByAge})
            context.params.addCoverage = {
                ...context.params.addCoverage,
                added: rateByAge,
                existing,
                errors: errs,
                updated: Date.now()
            }

        } else {
            context.result = _set(context.result, '_fastjoin.addCoverage', context.params.addCoverage);
        }
    }
    return context;
}

type FixFixOptions = {
    maxAge?: number;
    existing?: AnyObj
}
export const fixFixedRates = (premium: any, {maxAge, existing}: FixFixOptions) => {
    const {fixedRates} = premium || {}
    const rates = {...fixedRates}
    const rateBreak = premium.rateBreak || existing?.rateBreak || 'breakpoint';


    /** initially run through to assign prices from breakpoint to breakpoint */
    if (rateBreak === 'breakpoint') {
        const ages = premium.breakpointAges || existing?.breakpointAges || Object.keys(fixedRates).map(a => Number(a));
        for (let i = 0; i < ages[0]; i++) {
            rates[i] = fixedRates[ages[0]]
        }
        for (let i = 0; i < ages.length; i++) {
            for (let age = ages[i]; age < (ages[i + 1] || 120); age++) {
                rates[age] = fixedRates[ages[i]]
            }
        }
        const max = maxAge || 120
        for (let i = ages[ages.length - 1]; i <= max; i++) {
            rates[i] = fixedRates[ages[ages.length - 1]];
        }
        /** Set breakpoint ages - for display */
        premium.breakpointAges = ages;
    } else {
        /** initially run through to assign graduated pricing */
        let lastPrice;
        let lowestPrice;
        const keys = ['single', 'plus_spouse', 'plus_child', 'plus_child__2', 'plus_child__3']
        let emptyStretch: any = [];
        for (let i = 0; i < 120; i++) {
            if (fixedRates[i]?.single) {
                /**if we've had a gap - pro-rate each age between the last price and the current one. If there was no last price, set each empty price to the current price.*/
                if (emptyStretch.length) {
                    for (let idx = 0; idx < emptyStretch.length; idx++) {
                        const graduatedAge: any = {};
                        for (const k of keys) {
                            /** subtract the average difference in the empty stretch from each age - graduating to the current rate */
                            const topRate = fixedRates[i][k]
                            const lastRate = (lastPrice || {})[k] || topRate;
                            if (topRate && lastRate) graduatedAge[k] = topRate - (lastRate / (emptyStretch.length - idx))
                        }
                        rates[emptyStretch[idx]] = graduatedAge;
                    }
                }
                emptyStretch = [];
                if (!lowestPrice) lowestPrice = fixedRates[i]
                lastPrice = fixedRates[i]

            } else {
                emptyStretch.push(i)
            }
        }
        /** assign all top-end ages the highest age specified price */
        if (emptyStretch.length && lastPrice) {
            for (let i = 0; i < emptyStretch.length; i++) {
                rates[emptyStretch[i]] = lastPrice;
            }
        }
    }
    premium.fixedRates = rates;
    return premium;
}
export const fillFixedRates = async (context: HookContext): Promise<HookContext> => {
    let premium = context.data.premium || context.data.$set?.premium;
    if(premium && premium.fixedRates){
        let existing = context.data;
        if(context.method !== 'create'){
            existing = await loadExists(context);
            context = setExists(context, existing);
        }
        let maxAge = 120;
        if(context.path === 'rates'){
            const id = context.data.coverage || existing.coverage;
            existing = await new CoreCall('coverages', context, { skipJoins: true}).get(id)
                .catch(err => console.log(`Error getting coverage for max age on rate save: ${err.message}`));
            maxAge = existing.maxAge || 120;
            context.params.max_rate_age = maxAge;
            context.params.rate_coverage = existing;
        } else maxAge = context.data.maxAge || context.data.$set?.maxAge || existing?.maxAge || 120
        const newPremium = fixFixedRates(context.data.premium || context.data.$set.premium, { existing:existing?.premium, maxAge })
        if(context.data.premium) context.data.premium = newPremium;
        else context.data.$set.premium = newPremium;
    }

    return context;
}
