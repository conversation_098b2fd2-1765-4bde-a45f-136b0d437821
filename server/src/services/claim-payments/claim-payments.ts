// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    claimPaymentsDataValidator,
    claimPaymentsPatchValidator,
    claimPaymentsQueryValidator,
    claimPaymentsResolver,
    claimPaymentsExternalResolver,
    claimPaymentsDataResolver,
    claimPaymentsPatchResolver,
    claimPaymentsQueryResolver
} from './claim-payments.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {ClaimPaymentsService, getOptions} from './claim-payments.class.js'
import {claimPaymentsPath, claimPaymentsMethods} from './claim-payments.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {dollarString} from '../../../utils/simple.js';
import {getJoin} from '../../../utils/fast-join.js';
import {logChange} from '../../../utils/change-log.js';
import {relate} from '../../../utils/relate/index.js';

export * from './claim-payments.class.js'
export * from './claim-payments.schema.js'

const relateClaim = async (context: HookContext): Promise<HookContext> => {
    return relate('otm', {
        thereService: context.result?.service,
        herePath: 'claim',
        therePath: 'payments',
        paramsName: 'paymentsToClaim'
    })(context)
}

const authenticate = async (context: HookContext) => {
    const writer = [['payments', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['payments', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['from/owner'], '*']],
        or: '*'
    })(context) as any;
}

const relateEnrollment = async (context: HookContext): Promise<HookContext> => {
    return await relate('otm', {
        herePath: 'enrollment',
        therePath: 'claimPayments',
        thereService: 'enrollments'
    })(context);
}

const runJoins = async (context: HookContext) => {
    const {cp_org, cp_provider, cp_plan, cp_claim} = context.params.runJoin || {};
    if (cp_org) context = await getJoin({herePath: 'org', service: 'orgs'})(context)
    if (cp_provider) context = await getJoin({herePath: 'provider', service: 'providers'})(context)
    if (cp_plan) context = await getJoin({herePath: 'plan', service: 'plans'})(context)
    if (cp_claim) context = await getJoin({herePath: 'claim', service: 'claims'})(context)
    return context;
}

const protect = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;
    const ex = await loadExists(context);
    context = setExists(context, ex);
    if (ex.status === 'paid' && ex.confirmation) {
        const $set: any = {};
        let run = false;
        const sets = {...context.data, ...context.data.$set};
        const allow = ['claim', 'visit', 'enrollment', 'coverage'];
        for (let i = 0; i < allow.length; i++) {
            if (sets[allow[i]]) {
                $set[allow[i]] = sets[allow[i]];
                run = true;
            }
        }
        if (run) context.data = {$set}
        else throw new Error('You can only make limited changes to a completed claim payment')
    }
    return context;
}

const inc = async (context: HookContext): Promise<HookContext> => {
    const sets = {...context.data, ...context.data.$set}
    if (sets.status || sets.refunds || context.data.$addToSet?.refunds || context.data.$pull?.refunds) {
        await new CoreCall('claims', context, {skipJoins: true}).get(context.result.claim, {banking: {sync_claims_payments: true}})
            .catch(err => console.error(`Error syncing claim on claim payment status change: ${err.message}`));
        if (context.result.enrollment) await new CoreCall('enrollments', context, {skipJoins: true}).get(context.result.enrollment, {banking: {sync_claims_payments: true}})
            .catch(err => console.error(`Error syncing enrollment on claim payment status change: ${err.message}`));
    }
    return context;
}

const checkClaimsAdj = (cp:any) => {
    return async (context: HookContext): Promise<HookContext> => {
        const claims = await new CoreCall('claims', context, {skipJoins: true}).find({
            query: {
                $limit: cp.length,
                _id: {$in: cp.map(a => a.claim)}
            }
        })
            .catch(err => {
                console.error(`Error loading claims to check permissions for claim payments: ${err.messagge}`)
            })
        const byId: any = {};
        for (let i = 0; i < claims.data.length; i++) {
            byId[claims.data[i]._id] = claims.data[i]
        }
        for (let i = 0; i < cp.length; i++) {
            const {amount, claim: claimId} = cp[i];
            const claim = byId[claimId];
            if (!claim.adj?.adjAt || amount > claim.adj?.total) throw new Error(`Claim ${claim._id} for ${dollarString(claim.total / 100, '$', 2)} has not been approved by the plan for payment of ${dollarString(amount / 100, '$', 2)}`)
        }
        return claims;
    }
}

import { methods } from './utils/payment-methods.js';
type PayClaims = {
    moov_id: string,
    data: any,
    claimPayments: Array<string>,
    method: 'ca' | 'cc' | 'ach' | 'ext',
    payer: 'plan' | 'participant'
}
const process = async (context: HookContext): Promise<HookContext> => {
    const {pay_claims} = (context.params.banking || {}) as { pay_claims?: PayClaims } & any
    if (pay_claims) {
        const alreadyAdded:any[] = [];
        const needToBeAdded:any[] = [];
        for(let i = 0; i < pay_claims.claimPayments.length; i++) {
            if(pay_claims.claimPayments[i]._id) alreadyAdded.push(pay_claims.claimPayments[i]);
            else needToBeAdded.push(pay_claims.claimPayments[i]);
        }
        const added = await new CoreCall('claim-payments', context, {skipJoins: true}).create(needToBeAdded) as Array<any>
        if(pay_claims.payer === 'plan') await checkClaimsAdj([...added, ...alreadyAdded] as any[])(context);
        const transaction = await methods(context)[pay_claims.method].run(pay_claims.moov_id, pay_claims.data)
        context.result = await new CoreCall('claim-payments', context, {skipJoins: true}).patch(null, methods(context)[pay_claims.method].patch(transaction, pay_claims.data), { query: { _id: { $in: [...added, ...alreadyAdded].map(a => a._id)}}})

    }
    return context;

}


// A configure function that registers the service and its hooks via `app.configure`
export const claimPayments = (app: Application) => {
    // Register our service on the Feathers application
    app.use(claimPaymentsPath, new ClaimPaymentsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: claimPaymentsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(claimPaymentsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(claimPaymentsExternalResolver),
                schemaHooks.resolveResult(claimPaymentsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(claimPaymentsQueryValidator),
                schemaHooks.resolveQuery(claimPaymentsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                process,
                schemaHooks.validateData(claimPaymentsDataValidator),
                schemaHooks.resolveData(claimPaymentsDataResolver),
                relateClaim,
                relateEnrollment
            ],
            patch: [
                protect,
                schemaHooks.validateData(claimPaymentsPatchValidator),
                schemaHooks.resolveData(claimPaymentsPatchResolver),
                relateClaim,
                relateEnrollment
            ],
            remove: [
                relateClaim,
                relateEnrollment
            ]
        },
        after: {
            all: [runJoins],
            create: [relateClaim, relateEnrollment],
            patch: [relateClaim, relateEnrollment, inc],
            remove: [relateClaim, relateEnrollment]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [claimPaymentsPath]: ClaimPaymentsService
    }
}
