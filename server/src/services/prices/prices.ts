// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    pricesDataValidator,
    pricesPatchValidator,
    pricesQueryValidator,
    pricesResolver,
    pricesExternalResolver,
    pricesDataResolver,
    pricesPatchResolver,
    pricesQueryResolver
} from './prices.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {PricesService, getOptions} from './prices.class.js'
import {pricesPath, pricesMethods} from './prices.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, noThrow, setExists} from 'feathers-ucan';
import {getJoin} from '../../../utils/fast-join.js';
import {logChange} from '../../../utils/change-log.js';
import {relate} from '../../../utils/relate/index.js';

export * from './prices.class.js'
export * from './prices.schema.js'

const joinProvider = async (context: HookContext): Promise<HookContext> => {
    if (context.params.runJoin?.priceProvider) {
        return getJoin({
            herePath: 'provider',
            service: 'providers'
        })(context)
    }
    return context;
}

const relatePb = async (context: HookContext): Promise<HookContext> => {
    return relate('otm', {
        herePath: 'bundle',
        therePath: 'prices',
        thereService: 'bundles',
        paramsName: 'bundlePrices'
    })(context)
}
const authenticate = async (context: HookContext) => {
    const writer = [['prices', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['prices', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        create: noThrow,
        patch: writer,
        update: writer,
        remove: deleter
    };

    const cap_subjects:any = []
    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {
            const existing = await loadExists(context, {params: {runJoin: {priceProvider: true}}});
            context = setExists(context, existing);
            const orgId = existing?._fastjoin?.provider?.org
            if(orgId) {
                cap_subjects.push(orgId)
                const orgNamespace = `orgs:${orgId}`;
                const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'providerAdmin']]
                for (const w of orgWrite) {
                    ucanArgs.patch.unshift(w);
                    ucanArgs.update.unshift(w);
                    ucanArgs.remove.unshift(w);
                }
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects
    })(context) as any;
}

const dedupBook = async (context: HookContext): Promise<HookContext> => {
    if (!context.params.core.skipJoins && context.data.provider && context.data.bundle) {
        const {provider, bundle, code} = context.data
        const ex = await new CoreCall('prices', context, {skipJoins: true}).find({
            query: {
                provider,
                bundle,
                code,
                $limit: 1
            }
        })
        if (ex.total) throw new Error(`Price with code ${code} already exists for this bundle`)
    }
    return context;
}

const addProvider = async (context: HookContext): Promise<HookContext> => {
    if (!context.data.provider && context.data.bundle) {
        const pb = await new CoreCall('bundles', context).get(context.data.bundle);
        if (pb.provider) context.data.provider = pb.provider;
    }
    return context;
}



const updatePb = async (context: HookContext): Promise<HookContext> => {
    if (context.result.bundle && (context.method === 'create' || context.data.price || context.data.$set?.price || context.data.$inc?.price)) {
        await new CoreCall('bundles', context).patch(context.result.bundle, {updatedAt: new Date()}, {update_bundle_price: true})
            .catch(err => {
                console.log('failed to update bundle price for ', context.result._id, err.message);
            })
    }
    return context;
}

const price_check = async (context: HookContext): Promise<HookContext> => {
    const {price_check} = context.params.runJoin || {};
    if (price_check) {
        const {codes, states} = price_check;
        const prices = await new CoreCall('prices', context).find({
            query: {$limit: context.params.query?.$limit || 25, $sort: {price: 1}, code: {$in: codes}},
            pipeline: [
                {
                    $lookup: {
                        from: "bundles", // Assuming bundles is the collection name
                        localField: "bundle", // price.bundle is an ObjectId
                        foreignField: "_id",
                        as: "bundleDetails"
                    }
                },
                // Step 3: Unwind bundles for further processing
                {
                    $unwind: "$bundleDetails"
                },
                // Step 4: Lookup provider details for the bundles
                {
                    $lookup: {
                        from: "providers", // Assuming providers is the collection name
                        localField: "bundleDetails.provider", // bundle.provider is an ObjectId
                        foreignField: "_id",
                        as: "providerDetails"
                    }
                },
                // Step 5: Filter providers based on location.region matching the given state
                {
                    $unwind: "$providerDetails"
                },
                {
                    $addFields: {
                        "bundleDetails.fullProvider": "$providerDetails",
                        "bundleDetails.matchState": {
                            $gt: [
                                {
                                    $size: {
                                        $setIntersection: [
                                            states, // The array of states
                                            {
                                                $map: {
                                                    input: "$providerDetails.locations", // Iterate over provider.locations array
                                                    as: "location",
                                                    in: "$$location.region" // Extract the region field
                                                }
                                            }
                                        ]
                                    }
                                },
                                0// Match if the intersection has at least one element
                            ]
                        }
                    }
                },
                //  Group results to collect allBundles and stateBundles
                {
                    $addFields: {
                        "fullBundle": "$bundleDetails", // Embed the enriched bundle into the price
                        "stateMatch": "$bundleDetails.matchState" // Optional: Indicate if the bundle matches the state criteria
                    }
                }
            ]
        })
        // console.log('got prices', prices);
        // const data = [];
        // const { allBundles = [], stateBundles = [] } = prices.data[0] || {}
        // for(let i = 0; i < allBundles.length; i++){
        //     const { originalPrice, ...rest } = allBundles[i];
        //     const obj = { ...originalPrice, bundle: a, stateBundle: false };
        //     for(let idx = 0; idx < stateBundles.length; idx++) {
        //         if(stateBundles[idx]._id === rest._id){
        //             obj.stateBundle = true;
        //         }
        //     }
        // }
        // const data = prices.data[0]?.allBundles.map(a => {
        //
        //     return {
        //         ...originalPrice,
        //         bundle: a
        //     }
        // })
        context.result = prices;
        context.result.total = context.result.data.length
    }
    return context;
}

const codePricing = async (context: HookContext): Promise<HookContext> => {
    const { code_pricing } = context.params.runJoin || {};
    if(code_pricing){
        const { codes, rxcuis } = code_pricing;
        const data:any = [];
        let total = 0;
        const getOne = async (path:'code'|'rxcui', val:string) => {
            const res = await new CoreCall('prices', context)._find({ skip_hooks: true, admin_pass: true, query: { ...context.params.query, $limit: 3, $sort: { price: 1 }, [path]: val }})
                .catch(err => console.error(`Could not find ${path} ${val}: ${err.message}`))
            if(res.total) {
                total += res.total;
                for(let i = 0; i < res.data.length; i++){
                    data.push(res.data[i])
                }
            }
        }

        const promises:any = [];
        for(let i = 0; i < (codes || []).length; i++){
            promises.push(getOne('code', codes[i]))
        }
        for(let i = 0; i < (rxcuis || []).length; i++){
            promises.push(getOne('rxcui', rxcuis[i]))
        }
        await Promise.all(promises);
        context.result = { data, total }
    }
    return context;
}


import multer from 'multer';
const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    req.feathers.file = req.file;
    req.feathers.runJoin = req.query.runJoin;
    req.feathers.core = req.query.core;
    return next();
}
import { providerPriceUpload } from './utils/price-upload.js'
// A configure function that registers the service and its hooks via `app.configure`

import {getRelatedNdcs} from '../meds/meds.js';

const convertNdc = (context:HookContext) => {
    const run = (data) => {
        if(!data.ndc) return data;
        const { ndc11, ndc10 } = decideNdc(data.ndc);
        data.ndc11 = ndc11;
        data.ndc10 = ndc10;
        return data;
    }
    if(Array.isArray(context.data)) context.data = context.data.map(a => run(a))
    else context.data = run(context.data);
    return context;
}

const checkNdc = (context: HookContext) => {
    if(context.result.ndc && (!context.result.ndc10 || !context.result.ndc11) && !context.params.skip_hooks){
        const { ndc10, ndc11 } = decideNdc(context.result.ndc);
        context.result.ndc10 = ndc10;
        context.result.ndc11 = ndc11;
        new CoreCall('prices', context)._patch(context.result._id, { ndc10, ndc11 }, { skip_hooks: true, admin_pass: true})
            .catch(err => console.log(`Could not patch ndc10/ndc11 after hook. price id: ${context.result._id}: ${err.message}`))
    }
    return context;
}

const relatedNdcs = async (context: HookContext) => {
    const { ndc11, relatedCheckedAt } = context.result || {};
    if(ndc11 && !relatedCheckedAt || (new Date().getTime() - new Date(relatedCheckedAt).getTime() > 1000 * 60 * 60 * 24 * 180)){
        const related = await getRelatedNdcs(ndc11)
            .catch(err => console.log(`Could not auto sync related NDCs for price:${context.result._id}. ${err.message}`))
        if(related){
            context.result = await new CoreCall('prices', context).patch(context.result._id, { relatedCheckedAt: new Date(), $addToSet: { ndcs: { $each: related.splice(0, 25).map(a => a.ndc11) }}})
                .catch(err => {
                    console.log(`Error adding related NDCs for price ${context.result._id}. ${err.message}`)
                    return context.result;
                })
        }
    }
    return context;
}
export const prices = (app: Application) => {
    // Register our service on the Feathers application
    app.use(pricesPath,
        restMiddleware,
        new PricesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: pricesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(pricesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(pricesExternalResolver),
                schemaHooks.resolveResult(pricesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(pricesQueryValidator),
                schemaHooks.resolveQuery(pricesQueryResolver)
            ],
            find: [
                price_check,
                codePricing
            ],
            get: [],
            create: [
                providerPriceUpload,
                addProvider,
                convertNdc,
                schemaHooks.validateData(pricesDataValidator),
                schemaHooks.resolveData(pricesDataResolver),
                relatePb,
                dedupBook
            ],
            patch: [
                convertNdc,
                schemaHooks.validateData(pricesPatchValidator),
                schemaHooks.resolveData(pricesPatchResolver),
                relatePb
            ],
            remove: [relatePb]
        },
        after: {
            all: [joinProvider],
            find: [],
            get: [checkNdc],
            create: [relatedNdcs, relatePb, updatePb, providerPriceUpload],
            patch: [relatedNdcs, relatePb, updatePb, checkNdc],
            remove: [relatePb]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [pricesPath]: any
    }
}
