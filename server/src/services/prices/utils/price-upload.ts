import {HookContext} from '../../../declarations.js';
import {parseSheet} from '../../uploads/parser/index.js';
import {CoreCall} from 'feathers-ucan';
import {_get} from '../../../../utils/dash-utils.js';
import {fakeId} from '../../../../utils/simple.js';

export const providerPriceUpload = async (context: HookContext) => {
    const {price_upload} = context.params.runJoin || {};
    if (price_upload) {
        if (context.type === 'before') {

            const obj:any = {};
            for(const k of ['createdAt', 'updatedAt', 'createdBy', 'updatedBy', 'updatedByHistory', 'host', 'ref']){
                if(context.data[k]) obj[k] = context.data[k]
            }
            context.data = obj;
            // type Headers = { [key:string]: number}
            const headers:any = price_upload.headers;
            const headerKeys = Object.keys(headers);

            let errs: { row: number, data: any, key: string, err: string, throw?: boolean }[] = [];


            const format = (row, key, data) => {
                return {
                    'code': (val) => {
                        if (!val && !data[headers.rxcui] && !data[headers.ndc]) {
                            errs.push({row, key, err: 'Need one of RXCUI, Code, or NDC', throw: true, data})
                            return undefined
                        }
                        return val.trim();
                    },
                    'rxcui': (val) => {
                        if (!val && !data[headers.code] && !data[headers.ndc]) {
                            errs.push({row, key, err: 'Need one of RXCUI, Code, or NDC', throw: true, data})
                            return undefined
                        }
                        return val.trim();
                    },
                    'ndc': (val) => {
                        if (!val && !data[headers.code] && !data[headers.rxcui]) {
                            errs.push({row, key, err: 'Need one of RXCUI, Code, or NDC', throw: true, data})
                            return undefined
                        }
                        return val.split('-').join('').trim();
                    },
                    'billing_code': (val) => {
                        return val?.trim() || undefined
                    },
                    'providerName': (val) => {
                        return val?.trim() || undefined
                    },
                    's_f': (val) => {
                        return val?.trim() || undefined
                    },
                    'price': (val) => {
                        if (!val || !['number', 'string'].includes(typeof val)) {
                            errs.push({row, key, err: 'No Price', throw: true, data: val})
                            return undefined;
                        } else if (typeof val === 'number') return val * 100;
                        else if (typeof val === 'string') return Number(val.replace(/[^\d.]/g, '')) * 100
                    },
                    'name': (val) => {
                        if (!val || typeof val !== 'string') {
                            errs.push({row, key, err: 'No Name', throw: false, data: val})
                            return undefined;
                        } else return val.trim()
                    },
                    'description': (val) => val,
                    'provider': (val) => {
                        if(price_upload.provider) return price_upload.provider;
                        const v = val ? String(val).trim() : undefined;
                        if (v && v.length !== 24) {
                            errs.push({row, key, err: 'Invalid provider id', throw: false, data: val})
                            return undefined;
                        }
                        return v || price_upload.provider
                    },
                    'bundle': (val) => {
                        if(price_upload.bundle) return price_upload.bundle;
                        const v = val ? String(val).trim() : undefined;
                        if (v && v.length !== 24) {
                            errs.push({row, key, err: 'Invalid bundle id', throw: false, data: val})
                            return undefined;
                        }
                        return v || price_upload.bundle
                    }
                }
            }

            const getPrice = (val: any, row, addrs) => {
                const obj = { ...addrs };
                for (const key of headerKeys) {
                    obj[key] = format(row, key, val)[key](_get(val, headers[key]));
                }
                return obj
            }
            let prices: any = [];

            const runLimit = async (data: Array<any>, batch?:string) => {
                let priceList = data.map((a, i) => getPrice(a, i, { batch, ...price_upload.addrs}))


                // if (price_upload.ai_assist) {
                //     errs = [];
                //     const {key, org} = context.app.get('openai');
                //     const openai = new OpenAi({apiKey: key, organization: org})
                //     const result = await openai.chat.completions.create({
                //         // model: 'grok-2-latest',
                //         model: 'gpt-4o',
                //         messages: [
                //             {
                //                 role: 'system',
                //                 content: 'You are an assistant helping to normalize and cleanup healthcare procedure and drug data so it is more uniformly searchable'
                //             },
                //             {
                //                 role: 'user',
                //                 content: [
                //                     {
                //                         type: 'text',
                //                         text: `I have the following list of ${priceList.length} medical procedures. I am missing fields on some of the records. I want to have a "name", "description", "code" (CPT Code - if applicable), and "rxcui" (if applicable). code and especially rxcui may not be applicable to every item, "s_f" if there is an rxcui, we want to also get the strength and form (such as pill size and count) if discernible from the name/description. Here is the data that needs completion, infer the missing items from the other available properties. If not reasonably inferrable, leave it blank. The data is a JSON stringified array ${JSON.stringify(priceList)} --- Keep the id the same in each object so I can compare them. Return the list as a JSON list. No commentary and no markdown - just JSON`
                //                     },
                //                 ]
                //             }],
                //     })
                //         .catch(err => {
                //             console.log(`Error searching ai procedures: ${err.message}`)
                //         })
                //
                //     // console.log('result', result);
                //     const aiList =
                //         result ? JSON.parse((result.choices || [])[0]?.message?.content?.replace('```json', '').replace('```', '')?.trim().replace(/[']/g, '') || "{}") : {};
                //
                //     const priceObj: any = {};
                //     for (let i = 0; i < priceList.length; i++) {
                //         const {id, ...rest} = priceList[i];
                //         priceObj[id] = rest;
                //     }
                //     for (const item of aiList) {
                //         const {id, ...rest} = item;
                //         for (const k in rest) {
                //             if (item[k]) priceObj[id][k] = item[k];
                //         }
                //     }
                //     priceList = [];
                //     for (const k in priceObj) {
                //         const obj:any = {};
                //         for(const k2 in priceObj[k]){
                //             obj[k2] = format(k, k2)[k2](priceObj[k][k2])
                //         }
                //         priceList.push(obj);
                //     }
                // }

                const filteredPrices = priceList.filter((a, i) => a.price && !errs.some(b => b.row === i && b.throw));

                if (filteredPrices.length) {
                    const addedPrices = await new CoreCall('prices', context,).create(filteredPrices)
                        .catch(err => {
                            throw new Error(`Error adding prices: ${err.message}`)
                        })
                    for (let i = 0; i < (addedPrices || []).length; i++) {
                        prices.push(addedPrices[i]);
                    }
                }
            }

            const sheet = parseSheet(context.params.file.buffer, price_upload.sheet);
            const sliceCount = price_upload.omitFirstRow ? 1 : 0;

            const data = sheet.data.slice(sliceCount);
            const limit = 200;
            let completed = 0;
            const length = sheet.data.length;
            const batch = String(new Date().getTime())
            do {
                await runLimit(data.slice(completed, completed + limit), batch)
                    .catch(async err => {
                        console.log(`Error in looping limits for price upload: ${err.message}`)
                    });
                completed += limit;
            } while (completed < length);

            context.params.price_upload = {
                added: prices.length,
                errors: errs,
                updated: Date.now(),
                prices: prices,
                data: {}
            }
            if(prices?.length) prices[0]._fastjoin = { price_upload: context.params.price_upload }
            context.result = { _id: fakeId, data: prices, total: length, errors: errs };
            return prices;
        } else {
            if (context.result[0]) context.result[0]._fastjoin = {price_upload: context.params.price_upload}
        }
    }
}
