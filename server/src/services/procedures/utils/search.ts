import {HookContext} from '../../../declarations.js';
import axios from 'axios';
import {CoreCall} from 'feathers-ucan';
import { getMedInfo, getMedNdcs } from '../../meds/meds.js';
import {ObjectId} from 'mongodb';

const addMedsInfo = async (meds:Array<any>) => {
    const fullMed = async (med) => {
        if(med?.rxcui) {
            const medInfo = await getMedInfo(med.rxcui);
            med.info = medInfo;
            const ndcs = await getMedNdcs(med.rxcui);
            med.ndcs = [];
            (ndcs || []).forEach(a => {
                const { ndc10, ndc11 } = decideNdc(a)
                if(ndc10) med.ndcs.push(ndc10)
                if(ndc11) med.ndcs.push(ndc11)
            });
        }
        return med;
    }
    return Promise.all(meds.map(a => fullMed(a)));
}

const getAtIdx = (val, path, idx) => (val[path] || {[idx]: undefined})[idx]
const search_config = {
    'conditions': {
        codePath: 'code',
        baseUrl: 'https://clinicaltables.nlm.nih.gov/api/icd11_codes/v3/search',
        ef: 'code,title,definition,type,chapter,browserUrl',
        map: {
            name: (val, idx) => getAtIdx(val, 'title', idx),
            description: (val, idx) => getAtIdx(val, 'definition', idx),
            type: (val, idx) =>  getAtIdx(val, 'type', idx),
            code: (val, idx) => getAtIdx(val, 'code', idx),
            standard: () => 'icd11',
            chapter: (val, idx) => getAtIdx(val, 'chapter', idx),
            link: (val, idx) => getAtIdx(val, 'browserUrl', idx),
        },
    },
    'meds': {
        transform: addMedsInfo,
        codePath: 'rxcui',
        baseUrl: 'https://clinicaltables.nlm.nih.gov/api/rxterms/v3/search',
        ef: 'DISPLAY_NAME,STRENGTHS_AND_FORMS,RXCUIS,SXDG_RXCUI,DISPLAY_NAME_SYNONYM',
        map: {
            name: (val, idx) => getAtIdx(val, 'DISPLAY_NAME', idx),
            medical_name: (val, idx) => getAtIdx(val, 'SXDG_RXCUI', idx),
            s_f: (val, idx) => {
                const arr1 = getAtIdx(val, 'RXCUIS', idx);
                const arr2 = getAtIdx(val, 'STRENGTHS_AND_FORMS', idx)
                const obj = {};
                for(let i = 0; i < arr1?.length; i++){
                    obj[arr1[i]] = arr2[i]
                }
                return obj;
            },
            standard: () => 'rxcui',
            rxcui: (val, idx) => getAtIdx(val, 'SXDG_RXCUI', idx),
            synonyms: (val, idx) => {
                const v:any = getAtIdx(val, 'DISPLAY_NAME_SYNONYM', idx);
                if(Array.isArray(v)) return v.join(';')
                return v;
            },
            path: () => undefined
        },
    }
}
export const searchApi = (path: 'conditions' | 'procedures' | 'meds') => {
    return async (context: HookContext): Promise<HookContext> => {
        if(context.params.skip_hooks) return context;
        const {_search} = context.params;
        if (_search) {
            const diff = (context.params.query.$limit || 7) - context.result.total || 0
            if (diff > 0) {

                const params = {
                    'conditions': (defs) => {
                        return {
                            type: context.params.query?.type || 'stem',
                            ...defs
                        }
                    },
                    'meds': (defs) => defs
                }

                const settings = search_config[path];
                const {map} = settings;
                const res = await axios.get(settings.baseUrl, {
                    params: params[path]({
                        maxList: diff,
                        terms: _search.terms,
                        ef: settings.ef
                    })
                })
                const mapRes = (resObj) => {
                    const list: any[] = [];
                    for (const idx in res.data[1]) {
                        const obj = {};
                        for (const k in map) {
                            obj[k] = map[k](resObj, idx);
                        }
                        list.push(obj);
                    }
                    return list;
                }
                let extras = mapRes(res.data[2]).filter(a => a[settings.codePath] && a[settings.codePath] !== 'NA');
                if (extras?.length) {
                    const extraCodes = extras.map(a => a[settings.codePath]);
                    const existing = await new CoreCall(context.path, context).find({
                        query: {
                            $limit: extraCodes.length,
                            [settings.codePath]: {$in: extraCodes}
                        }
                    });
                    for (const ex of existing.data) {
                        const idx = extraCodes.indexOf(ex[settings.codePath]);
                        if (idx > -1) {
                            extras.splice(idx, 1);
                            extraCodes.splice(idx, 1);
                        }
                    }
                    if (extras?.length) {
                        if(settings.transform) extras = await settings.transform(extras);
                        const moreExisting = await new CoreCall(context.path, context)._find({ skip_hooks: true, admin_pass: true, query: { deleted: { $ne: true }, [settings.codePath]: { $in: extraCodes }, $limit: extraCodes.length }})
                        if(moreExisting.total){
                            for(let i = moreExisting.data.length - 1; i >= 0; i--){
                                const drg = moreExisting.data[i];
                                const idx = extraCodes.indexOf(drg[settings.codePath]);
                                if(idx > -1) extras.splice(idx, 1);
                                existing.data.push(drg)
                            }
                        }
                        if(extras.length) await new CoreCall(context.path, context)._create(extras, {admin_pass: true, skip_hooks: true})
                            .catch(err => {
                                console.error(`Failed to create ${context.path} from search: ${err.message}`)
                                return [];
                            })
                    }
                    const {$limit, $skip, $or, $sort, ...rest} = context.params.query;
                    context.result = await new CoreCall(context.path, context).find({
                        query: {
                            $limit,
                            $skip,
                            $sort,
                            $or: [
                                ...$or || [],
                                {...rest},
                                {_id: {$in: existing.data.map(a => typeof a._id === 'string' ? ObjectId.createFromHexString(a._id) : a._id)}}
                            ]
                        }
                    })
                }
            }
        }
        return context;
    }
}
