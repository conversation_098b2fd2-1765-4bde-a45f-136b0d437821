// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    networksDataValidator,
    networksPatchValidator,
    networksQueryValidator,
    networksResolver,
    networksExternalResolver,
    networksDataResolver,
    networksPatchResolver,
    networksQueryResolver
} from './networks.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {NetworksService, getOptions} from './networks.class.js'
import {networksPath, networksMethods} from './networks.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, checkUcan, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {logChange} from '../../../utils/change-log.js';

export * from './networks.class.js'
export * from './networks.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['networks', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['networks', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['managers/owner'], '*'], [['writers/owner'], ['patch', 'create']]],
        or: '*'
    })(context) as any;
}
import { addCreator } from '../cats/cats.js';

const handleInvite = async (context: HookContext) => {
        const {$addToSet} = context.data;
        if($addToSet) {
            const { plan_invites, bundle_invites } = $addToSet;
            /*If user is an admin for the plan, accept invite automatically and patch plan*/
            if(plan_invites){
                const ex = await loadExists(context);
                context = setExists(context, ex);
                const caps:Array<[string, string]> = [[`plans:${plan_invites}`, 'planAdmin'], [`plans:${plan_invites}`, 'medAdmin'], [`plans:${plan_invites}`, 'claimsAdmin']];
                let checkErr = false;
                if(ex.access !== 'public') await checkUcan(caps, { or: ['patch'] })(context)
                    .catch(() => {
                        checkErr = true;
                    })
                if(!checkErr) {
                    context.data.$addToSet.plans = plan_invites;
                    await new CoreCall('plans', context, { skipJoins: true }).patch(plan_invites, { $addToSet: { networks: context.id }})
                    delete context.data.$addToSet.plan_invites
                }
            }
            /*If user is an admin for the provider, accept invite automatically and patch bundle*/
            if(bundle_invites){

                const pb = await new CoreCall('bundles', context).get(bundle_invites, { runJoin: { pb_provider: true }})
                    .catch(err => console.error(`Could not get bundle on add bundle to network: ${err.message}`));
                if(pb?.provider){
                    const cap_subjects:any = []
                    const ex = await loadExists(context);
                    context = setExists(context, ex);
                    cap_subjects.push(pb.provider)
                    const caps:Array<[string, string]> = [[`providers:${pb.provider}`, 'providerAdmin'], [`providers:${pb.provider}`, 'billingAdmin']]
                    if(pb._fastjoin?.provider?.org) {
                        caps.push([`orgs:${pb._fastjoin.provider.org}`, 'orgAdmin'])
                        cap_subjects.push(pb._fastjoin.provider.org)
                    }
                    let checkErr = false;
                    if(ex.access !== 'public') await checkUcan(caps, { cap_subjects, or: ['patch'] })(context)
                        .catch(() => {
                            checkErr = true;
                        })
                    if(!checkErr){
                        context.data.$addToSet.bundles = bundle_invites;
                        await new CoreCall('bundles', context, { skipJoins: true }).patch(bundle_invites, { $addToSet: { networks: context.id }})
                        delete context.data.$addToSet.bundle_invites
                    }
                }
            }
        }
        return context;
}
const handleReq = async (context: HookContext) => {
    const {$addToSet} = context.data;
    const { plan_reqs, bundle_reqs } = $addToSet || {};

    //if requester has admin persmissions for then network, accept automatically
    if(plan_reqs || bundle_reqs) {
        const caps:Array<[string,string]> = [['networks', '*']];
        let checkErr = false;
        const ex = await loadExists(context);
        context = setExists(context, ex);
        if(ex.access !== 'public') await checkUcan(caps, { loginPass: [[['managers/owner'], '*'], [['writers/owner'], ['patch']]] })(context)
            .catch(() => checkErr = true);
        if(!checkErr){
            if(plan_reqs){
                await new CoreCall('plans', context, { skipJoins: true }).patch(plan_reqs,  { $addToSet: { networks: context.id }}, { admin_pass: true })
                context.data.$addToSet.plans = plan_reqs;
                delete context.data.$addToSet.plan_reqs
            }
            if(bundle_reqs){
                await new CoreCall('bundles', context, { skipJoins: true }).patch(bundle_reqs,  { $addToSet: { networks: context.id }}, { admin_pass: true })
                context.data.$addToSet.bundles = bundle_reqs;
                delete context.data.$addToSet.bundle_reqs
            }
        }
    }
    return context;
}

//ensure bundles and plans that have removed network are reflected here - however, network being on a bundle or plan does not mean it belongs here TODO: change to a filter function instead of setting the array to the result of this find - no client functionality exists to add a network to a bundle or plan directly, but still this logic is flawed
const runSync = async (context: HookContext) => {
    const { sync_bundles, sync_plans } = context.params.runJoin || {};
    if(sync_bundles || sync_plans && (!context.result.lastSync || new Date().getTime() - new Date(context.result.lastSync).getTime() > (1000 * 60)) ) {
        const patchObj:any = { lastSync: new Date() };
        if(sync_plans) {
            const plans = await new CoreCall('plans', context, { skipJoins: true }).find({ query: { networks: { $in: [context.result._id] }}, paginate: false, admin_pass: true })
                .catch(err => {
                    console.error(`Failed to sync plans for network id: ${context.result._id} - ${err.message}`)
                    return undefined
                })
            if(Array.isArray(plans)) patchObj.plans = plans.map(a => a._id)
        }
        if(sync_bundles){
            const pbs = await new CoreCall('bundles', context, { skipJoins: true }).find({ query: { networks: { $in: [context.result._id] }}, paginate: false })
                .catch(err => {
                    console.error(`Failed to sync bundles for network id: ${context.result._id} - ${err.message}`)
                    return undefined
                })
            if(Array.isArray(pbs)) patchObj.bundles = pbs.map(a => a._id)
        }
        const updated = await new CoreCall('networks', context, { skipJoins: true }).patch(context.result._id, patchObj, { admin_pass: true });
        context.result = { ...updated, _fastjoin: context.result._fastjoin }
    }
    return context;
}

const checkName = async (context:HookContext):Promise<HookContext> => {
    const name = context.data.name || context.data.$set?.name;
    if(name){
        const query:any = { name: name.trim() };
        if(context.id) query._id = { $ne: context.id }
        const nameMatch = await new CoreCall('networks', context, { skipJoins: true }).find({ query });
        if(nameMatch.total) throw new Error(`Network named "${name}" already exists`)
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const networks = (app: Application) => {
    // Register our service on the Feathers application
    app.use(networksPath, new NetworksService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: networksMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(networksPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(networksExternalResolver),
                schemaHooks.resolveResult(networksResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(networksQueryValidator),
                schemaHooks.resolveQuery(networksQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                addCreator,
                schemaHooks.validateData(networksDataValidator),
                schemaHooks.resolveData(networksDataResolver),
                checkName
            ],
            patch: [
                schemaHooks.validateData(networksPatchValidator),
                schemaHooks.resolveData(networksPatchResolver),
                checkName,
                handleInvite,
                handleReq
            ],
            remove: []
        },
        after: {
            all: [],
            get: [runSync],
            patch: [runSync]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [networksPath]: NetworksService
    }
}
