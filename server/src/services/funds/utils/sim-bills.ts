import {HookContext} from '../../../declarations.js';
import {
    simulateHealthcareSpend,
    Household,
    Coverage,
    CoverageLimits,
    Distribution,
    getAcaOop, getPrivateOop, Limit
} from '../../shops/sim/utils.js';
import {costDataConfig, distributionKeysConfig} from '../../shops/sim/cost-models/index.js'
import {quickTaxTotal} from '../../../utils/tax/tax-calcs.js';

type CoverageScores = {
    [key: string]: {
        top1: number, //had the lowest total spend
        top3: number, //had top3 lowest spend
        last: number, //had the worst spend
        pr: number, //best outcome for this coverage (range min)
        pr_spend: number, //spend total on best outcome
        pw: number, //worst outcome for this coverage (range max)
        pw_spend: number, //spend for worst outcome for this coverage
        premium: number, //premium for this coverage
        average: number, //average outcome
        median: number, //median outcome
        spend: number,
        oop: number,
        total: number,
        tax_savings: number,
        moop: Limit,
        deductible: Limit,
        coinsurance: number
    }
}
type SimBills = {
    simsCount: number,
    model: 'dental' | 'vision' | 'medical',
    household: Household,
    coverages: Array<Coverage>,
    risk?: number,
    coverage_limits: CoverageLimits,
    tax_rate?: number,
    modifiers?: string
}

export const simulateCommonFundsBills = ({
                                             simsCount = 100,
                                             model,
                                             household,
                                             coverages = [],
                                             risk,
                                             coverage_limits,
                                             tax_rate = 0,
                                             modifiers
                                         }: SimBills) => {
    return (context: HookContext) => {

        let children = 0;
        let spouse = -1;
        const enrolled: any = [];
        for (const prsn of household.people) {
            if (prsn.child || spouse > 0) {
                children++
                enrolled.push({age: prsn.age, relation: 'child'})
            } else {
                enrolled.push({age: prsn.age, relation: spouse > -1 ? 'spouse' : 'self'})
                spouse++
            }
        }

        const key = children ? spouse > 0 ? 'family' : `plus_children${children === 1 ? '' : children > 2 ? '__3' : '__2'}` : spouse > 0 ? 'plus_spouse' : 'single' as any

        let allSpend = 0;
        const byYear: number[] = []
        const spendList: Array<number> = []
        const distributionKeys = distributionKeysConfig[model]
        const costData = modifiers ? costDataConfig[model][modifiers] : costDataConfig[model]
        const coverage_scores: CoverageScores = {}

        const coverage_premiums: { [key: string]: number } = {}
        for (let i = 0; i < coverages.length; i++) {
            const coverage = coverages[i];
            coverage_limits[coverage._id] = coverage.acaPlan ? getAcaOop({policy: coverage}) : getPrivateOop({policy: coverage});
            const premium = getCoverageRate({coverage, enrolled}) * 12
            coverage_premiums[coverage._id] = premium;
            coverage_scores[coverage._id] = {...coverage_scores[coverage._id], premium}
        }

        const distribution: Distribution = {'*': {}}
        const dcount: Distribution = {'*': {}}
        for (let i = 0; i < distributionKeys.length; i++) {
            distribution[String(distributionKeys[i])] = {};
            dcount[String(distributionKeys[i])] = {};
        }
        const overCaps: any = {}
        for (let i = 0; i < simsCount; i++) {
            const {spend, byCoverageId, overCapByCoverageId} = simulateHealthcareSpend({
                household,
                coverages,
                key,
                risk,
                coverage_limits,
                costData
            })
            for (const k in overCapByCoverageId) {
                overCaps[k] = (overCaps[k] || 0) + overCapByCoverageId[k]
            }

            allSpend += spend;
            byYear.push(spend);
            let spent = false;
            for (let s = 0; s <= spendList.length; s++) {
                if (spend < spendList[s]) {
                    spendList.splice(s, 0, spend);
                    spent = true;
                    break;
                }
            }
            if (!spent) spendList.push(spend);


            let distribution_key = '*';
            for (let di = 0; di < distributionKeys.length; di++) {
                if (spend <= distributionKeys[di]) {
                    distribution_key = String(distributionKeys[di])
                    break;
                }
            }

            const best_worst: Array<[string, number]> = [];
            for (const k in byCoverageId) {
                const oop = byCoverageId[k];
                const total = oop + coverage_scores[k].premium;
                /** Log distribution for later setting averages */
                distribution[distribution_key][k] = (distribution[distribution_key][k] || 0) + total;
                dcount[distribution_key][k] = (dcount[distribution_key][k] || 0) + 1;

                /** AVERAGE & LOGS */
                coverage_scores[k].average = (coverage_scores[k].average || 0) + total
                /** END AVERAGE & LOGS */

                /** LOG SPEND TOTALS */
                best_worst.push([k, total])
            }
            const ranks: any = {};
            for (let idx = 0; idx < best_worst.sort((a, b) => a[1] - b[1]).length; idx++) {
                ranks[best_worst[idx][0]] = idx
            }

            /** Loop back through keys and set scores according to rank */
            for (const k in byCoverageId) {
                const rank = ranks[k];

                const ten_percent = Math.max(3, Math.ceil(best_worst.length * .1))
                /** Top 3 values */

                if (rank < ten_percent) {
                    coverage_scores[k].top3 = (coverage_scores[k].top1 || 0) + 1
                    if (rank === 0) {
                        coverage_scores[k].top1 = (coverage_scores[k].top1 || 0) + 1
                    }
                }
                /** END Top 3 */

                /** Last */
                const last3 = best_worst.length - ten_percent;
                if (rank >= last3) {
                    coverage_scores[k].last = (coverage_scores[k].last || 0) + 1
                }

            }

        }

        /**loop through distribution keys and average total*/
        for (const breakpoint in distribution) {
            for (const k in distribution[breakpoint]) {
                if (isNaN(distribution[breakpoint][k])) {
                    distribution[breakpoint][k] = 0;
                } else distribution[breakpoint][k] = Math.round(distribution[breakpoint][k] / dcount[breakpoint][k])
            }
        }
        /**loop through and divide average total by sims for actual average */
        for (const k in coverage_scores) {
            coverage_scores[k].average = Math.round(coverage_scores[k].average / simsCount);
        }

        const defSpend = () => {
            return {spend: 0, count: 0}
        }
        const spend_dist = {
            '1': defSpend(),
            '5': defSpend(),
            '10': defSpend(),
            '20': defSpend(),
            '50': defSpend(),
            '0': defSpend()
        };
        const spendListLength = spendList.length;
        for (const k of [1, 5, 10, 20, 50]) {
            const percent = Math.ceil(spendListLength * (k / 100))
            const arr = spendList.slice(-percent)
            spend_dist[String(k)] = {spend: Math.round(arr.reduce((acc, v) => v + acc)), count: arr.length}
        }
        spend_dist['0'] = {
            count: spendListLength - spend_dist['50'].count,
            spend: allSpend - spend_dist['50'].spend
        }
        const spend = allSpend / simsCount;

        let t_r = tax_rate || 0
        try {
            const hh_members = household.people
            const tax_obj = quickTaxTotal({
                income: household.income || 0,
                hh_members: hh_members.map(a => {
                    return {
                        age: a.age,
                        dependent: a.child || a.age < 18,
                        dob: a.dob
                    }
                }),
                filing_as: hh_members.some(a => a.relation === 'spouse') ? 'mj' : hh_members.length > 1 ? 'hh' : 's'
            })
            t_r = Math.max(0, tax_obj.rate);
            if (isNaN(t_r)) t_r = .06;
        } catch (e: any) {
            console.log(`Error calculating tax rate in cost sim: ${e.message}`)
        }

        for (let i = 0; i < coverages.length; i++) {
            let use_tax_rate = t_r + 0;
            if (coverages[i].type === 'hs') use_tax_rate = 0;
            const premium = coverage_premiums[coverages[i]._id];
            const tax_savings = Math.max(0, premium * (use_tax_rate + .153))
            const average = coverage_scores[coverages[i]._id].average
            const total = average - tax_savings;
            coverage_scores[coverages[i]._id] = {
                ...coverage_scores[coverages[i]._id],
                spend,
                oop: average - premium,
                total,
                premium,
                tax_savings,
                ...coverage_limits[coverages[i]._id],
            }
        }

        return {
            spend_dist,
            allSpend,
            byYear,
            worst10: spendList.slice(-10),
            distribution,
            coverage_scores,
            spend,
            tax_rate,
            lastRun: new Date(),
            simsCount,
            overCaps,
            coverages: coverages.map(a => {
                return {
                    _id: a._id,
                    name: a.name,
                    carrierName: a.carrierName,
                    carrierLogo: a.carrierLogo,
                    acaPlan: a.acaPlan,
                    type: a.type,
                    off_exchange: a.off_exchange,
                    on_exchange: a.on_exchange,
                    plan_id: a.plan_id,
                    deductible: a.deductible,
                    moop: a.moop,
                    coinsurance: a.coinsurance,
                    copys: a.copays,
                    coins: a.coins,
                    benefits: a.benefits
                }
            })
        }
    }
}

export const getSim = async (context: HookContext) => {
    const {sim_bills} = context.params.runJoin || {};
    if (sim_bills) {
        context.result = {_id: context.id};
        if (sim_bills.multi) {
            const obj: any = {};
            for (const k in sim_bills.multi) {
                obj[k] = simulateCommonFundsBills(sim_bills.multi[k])(context);
            }
            context.result._fastjoin = {sim: obj};
        } else context.result._fastjoin = {sim: simulateCommonFundsBills(sim_bills)(context)};
    }
    return context;
}

