import {CoreCall} from 'feathers-ucan';
const headers = {
    "BUSINESS YEAR": 0,
    "STATE CODE": 1,
    "ISSUER NAME": 2,
    "ISSUER ID": 3,
    "SOURCE NAME": 4,
    "VERSION NUMBER": 5,
    "IMPORT DATE": 6,
    "BENEFIT PACKAGE ID": 7,
    "MARKET COVERAGE": 8,
    "DENTAL ONLY PLAN": 9,
    "TIN": 10,
    "STANDARD COMPONENT ID": 11,
    "PLAN MARKETING NAME": 12,
    "HIOS PRODUCT ID": 13,
    "HPID": 14,
    "NETWORK ID": 15,
    "SERVICE AREA ID": 16,
    "FORMULARY ID": 17,
    "IS IT A NEW PLAN": 18,
    "PLAN TYPE": 19,
    "METAL LEVEL": 20,
    "UNIQUE PLAN DESIGN": 21,
    "QHP NONQHP TYPE ID": 22,
    "IS NOTICE REQUIRED FOR PREGNACY": 23,
    "IS REFERRAL REQUIRED FOR SPECIALIST": 24,
    "SPECIALIST REQUIRING REFERRAL": 25,
    "PLAN LEVEL EXCLUSIONS": 26,
    "IS HSA ELIGIBLE": 27,
    "HSA OR HRA EMPLOYER CONTRIBUTION": 28,
    "HSA OR HRA EMPLOYER CONTRIBUTION AMOUNT": 29,
    "CHILD ONLY OFFERING": 30,
    "CHILD ONLY PLAN ID": 31,
    "WELLNESS PROGRAM OFFERED": 32,
    "DISEASE MANAGEMENT PROGRAMS OFFERED": 33,
    "EHB PEDIATRIC DENTAL APPORTIONMENT QUANTITY": 34,
    "IS GUARANTEED RATE?": 35,
    "SPECIALITY DRUG MAXIMUM COINSURANCE": 36,
    "INPATIENT COPAYMENT MAXIMUM DAYS": 37,
    "BEGIN PRIMARY CARE COST SHARING AFTER NUMBER OF VISITS": 38,
    "BEGIN PRIMARY CARE DEDUCTIBLE COINSURANCE AFTER NUMBER OF COPAYS": 39,
    "PLAN EFFECTIVE DATE": 40,
    "PLAN EXPIRATION DATE": 41,
    "OUT OF COUNTRY COVERAGE": 42,
    "OUT OF COUNTRY COVERAGE DESCRIPTION": 43,
    "OUT OF SERVICE AREA COVERAGE": 44,
    "OUT OF SERVICE AREA COVERAGE DESCRIPTION": 45,
    "NATIONAL NETWORK": 46,
    "URL FOR SUMMARY OF BENEFITS COVERAGE": 47,
    "URL FOR ENROLLMENT PAYMENT": 48,
    "PLAN BROCHURE": 49,
    "FORMULARY URL": 50,
    "PLAN ID": 51,
    "CSR VARIATION TYPE": 52,
    "ISSUER ACTUARIAL VALUE": 53,
    "AV CALCULATOR OUTPUT NUMBER": 54,
    "MEDICAL DRUG DEDUCTIBLES INTEGRATED": 55,
    "MEDICAL DRUG MAXIMUM OUT OF POCKET INTEGRATED": 56,
    "MULTIPLE NETWORK TIERS": 57,
    "FIRST TIER UTILIZATION": 58,
    "SECOND TIER UTILIZATION": 59,
    "MEHB INN TIER 1 INDIVIDUAL MOOP": 60,
    "MEHB INN TIER 1 FAMILY MOOP": 61,
    "MEHB INN TIER 2 INDIVIDUAL MOOP": 62,
    "MEHB INN TIER 2 FAMILY MOOP": 63,
    "MEHB OUT OF NET INDIVIDUAL MOOP": 64,
    "MEHB OUT OF NET FAMILY MOOP": 65,
    "MEHB COMB INN OON INDIVIDUAL MOOP": 66,
    "MEHB COMB INN OON FAMILY MOOP": 67,
    "DEHB INN TIER 1 INDIVIDUAL MOOP": 68,
    "DEHB INN TIER 2 INDIVIDUAL MOOP": 69,
    "DEHB INN TIER 1 FAMILY MOOP": 70,
    "DEHB INN TIER 2 FAMILY MOOP": 71,
    "DEHB OUT OF NET INDIVIDUAL MOOP": 72,
    "DEHB OUT OF NET FAMILY MOOP": 73,
    "DEHB COMB INN OON INDIVIDUAL MOOP": 74,
    "DEHB COMB INN OON FAMILY MOOP": 75,
    "TEHB INN TIER 1 INDIVIDUAL MOOP": 76,
    "TEHB INN TIER 1 FAMILY MOOP": 77,
    "TEHB INN TIER 2 INDIVIDUAL MOOP": 78,
    "TEHB INN TIER 2 FAMILY MOOP": 79,
    "TEHB OUT OF NET INDIVIDUAL MOOP": 80,
    "TEHB OUT OF NET FAMILY MOOP": 81,
    "TEHB COMB INN OON INDIVIDUAL MOOP": 82,
    "TEHB COMB INN OON FAMILY MOOP": 83,
    "MEHB DED INN TIER1 INDIVIDUAL": 84,
    "MEHB DED INN TIER1 FAMILY": 85,
    "MEHB DED INN TIER1 COINSURNACE": 86,
    "MEHB DED INN TIER2 INDIVIDUAL": 87,
    "MEHB DED INN TIER2 FAMILY": 88,
    "MEHB DED INN TIER2 COINSURANCE": 89,
    "MEHB DED OUT OF NET INDIVIDUAL": 90,
    "MEHB DED OUT OF NET FAMILY": 91,
    "MEHB DED COMB INN OON INDIVIDUAL": 92,
    "MEHB DED COMB INN OON FAMILY": 93,
    "DEHB DED INN TIER1 INDIVIDUAL": 94,
    "DEHB DED INN TIER1 FAMILY": 95,
    "DEHB DED INN TIER1 COINSURNACE": 96,
    "DEHB DED INN TIER2 INDIVIDUAL": 97,
    "DEHB DED INN TIER2 FAMILY": 98,
    "DEHB DED INN TIER2 COINSURANCE": 99,
    "DEHB DED OUT OF NET INDIVIDUAL": 100,
    "DEHB DED OUT OF NET FAMILY": 101,
    "DEHB DED COMB INN OON INDIVIDUAL": 102,
    "DEHB DED COMB INN OON FAMILY": 103,
    "TEHB DED INN TIER 1 INDIVIDUAL": 104,
    "TEHB DED INN TIER 1 FAMILY": 105,
    "TEHB DED INN TIER 1 COINSURANCE": 106,
    "TEHB DED INN TIER 2 INDIVIDUAL": 107,
    "TEHB DED INN TIER 2 FAMILY": 108,
    "TEHB DED INN TIER 2 COINSURANCE": 109,
    "TEHB DED OUT OF NET INDIVIDUAL": 110,
    "TEHB DED OUT OF NET FAMILY": 111,
    "TEHB DED OUT COMB INN OON INDIVIDUAL": 112,
    "TEHB DED OUT COMB INN OON FAMILY": 113,
    "SBC HAVING A BABY DEDUCTIBLE": 114,
    "SBC HAVING A BABY COPAYMENT": 115,
    "SBC HAVING A BABY COINSURANCE": 116,
    "SBC HAVING A BABY LIMIT": 117,
    "SBC HAVING DIABETES DEDUCTIBLE": 118,
    "SBC HAVING  DIABETES COPAYMENT": 119,
    "SBC HAVING DIABETES COINSURANCE": 120,
    "SBC HAVING DIABETES LIMIT": 121,
    "EHB PERCENT PREMIUM": 122,
    "PLAN DESIGN TYPE": 123,
    "INSURANCE PLAN COMPOSITE PREMIUM AVAILABLE INDICATOR": 124,
    "PLAN VARIANT MARKETING NAME": 125,
    "SBC HAVING SIMPLE FRACTURE DEDUCTIBLE": 126,
    "SBC HAVING SIMPLE FRACTURE COPAYMENT": 127,
    "SBC HAVING SIMPLE FRACTURE COINSURANCE": 128,
    "SBC HAVING SIMPLE FRACTURE LIMIT": 129
}

import {_set, _get} from '../../../../utils/dash-utils.js';
import {getNetworkKey, blendCoins, defBen, getPolicyNames, getMetal} from '../../marketplace/cms/index.js';
import {HookContext} from '../../../declarations.js';

const sMax = costLimits.moop.single;
const fMax = costLimits.moop.family;

const maxes = {
    medical: {
        single: {
            in_network: sMax,
            in_network2: sMax,
        },
        family: {
            in_network: fMax,
            in_network2: fMax,
        }
    },
    drug: {
        single: {
            in_network: sMax,
            in_network2: sMax,
        },
        family: {
            in_network: fMax,
            in_network2: fMax,
        }
    }
}
// Function to parse MOOP and Deductible values from the CSV row
const parseMoopOrDeductible = (row: any, type: 'MOOP' | 'DED') => {


    let res: any = {
        medical: {
            single: {
                in_network: sMax,
            },
            family: {
                in_network: fMax,
            }
        },
        drug: {
            single: {
                in_network: sMax,
            },
            family: {
                in_network: fMax,
            }
        }
    };

    const splitter = (v) => {
        if(!v || typeof v !== 'string') return undefined
        return parseFloat(v.split(' ')[0].replace(/[^0-9.]/g, ''))
    }

    const setMedAndCombined = (rowPath:string, resPath:string) => {
        const tehbPath = `TEHB ${rowPath.split('TIER').join(' ')}`
        const mehb = row[`MEHB ${rowPath}`] || row[tehbPath]
        const tehb = row[tehbPath] || mehb
        // In-Network Tier 1
        if (mehb || tehb) {
            const splm = splitter(mehb)
            const splt = splitter(tehb)
            const m = splm || splm === 0 ? splm : splt
            const t = splt || splt === 0 ? splt : m
            if(isNaN(m as number)) return;
            const v = Math.min(m as number, t as number);
            if (!isNaN(v as number)) res = _set(res, resPath, v)
        }
    }
    const setDrug = (rowPath:string, resPath:string) => {
        const dehb = row[rowPath]
        if(dehb){
            const d = splitter(dehb);
            if(!isNaN(d as any)) res = _set(res, resPath, d);
        }
    }
    const setMoop = (rowPath:string, resPath:string) => {
        const dehb = row[rowPath]
        if(dehb){
            const d = splitter(dehb);
            if(!isNaN(d as any)) res = _set(res, resPath, Math.max(d as number, _get(maxes, resPath, d) as number));
        }
    }
    // Parse Medical values
    if (type === 'MOOP') {

        setMedAndCombined('INN TIER1 INDIVIDUAL MOOP', 'medical.single.in_network')
        setMedAndCombined('INN TIER1 FAMILY MOOP', 'medical.family.in_network')

        setMedAndCombined('INN TIER2 INDIVIDUAL MOOP', 'medical.single.in_network2')
        setMedAndCombined('INN TIER2 FAMILY MOOP', 'medical.family.in_network2')

        setMedAndCombined('OUT OF NET INDIVIDUAL MOOP', 'medical.single.oon')
        setMedAndCombined('OUT OF NET FAMILY MOOP', 'medical.family.oon')

        setDrug('DEHB INN TIER 1 INDIVIDUAL MOOP', 'drug.single.in_network')
        setDrug('DEHB INN TIER 1 FAMILY MOOP', 'drug.family.in_network')
        setDrug('DEHB INN TIER 2 INDIVIDUAL MOOP', 'drug.single.in_network2')
        setDrug('DEHB INN TIER 2 FAMILY MOOP', 'drug.family.in_network2')
        setDrug('DEHB OUT OF NET INDIVIDUAL MOOP', 'drug.single.oon')
        setDrug('DEHB OUT OF NET FAMILY MOOP', 'drug.family.oon')

    } else if (type === 'DED') {
        setMedAndCombined('DED INN TIER1 INDIVIDUAL', 'medical.single.in_network')
        setMedAndCombined('DED INN TIER1 FAMILY', 'medical.family.in_network')

        setMedAndCombined('DED INN TIER2 INDIVIDUAL', 'medical.single.in_network2')
        setMedAndCombined('DED INN TIER2 FAMILY', 'medical.family.in_network2')

        setMedAndCombined('DED OUT OF NET INDIVIDUAL', 'medical.single.oon')
        setMedAndCombined('DED OUT OF NET FAMILY', 'medical.family.oon')
        // In-Network Tier 1

        setDrug('DEHB DED INN TIER1 INDIVIDUAL', 'drug.single.in_network')
        setDrug('DEHB DED INN TIER1 FAMILY', 'drug.family.in_network')
        setDrug('DEHB DED INN TIER2 INDIVIDUAL', 'drug.single.in_network2')
        setDrug('DEHB DED INN TIER2 FAMILY', 'drug.family.in_network2')
        setDrug('DEHB DED OUT OF NET INDIVIDUAL', 'drug.single.oon')
        setDrug('DEHB DED OUT OF NET FAMILY', 'drug.family.oon')

    }

    // Ensure single values are not greater than family values
    res.medical.single.in_network = Math.min(res.medical.single.in_network || sMax, res.medical.family.in_network || sMax);
    res.medical.single.in_network2 = Math.min(res.medical.single.in_network2 || sMax, res.medical.family.in_network2 || sMax);
    res.medical.single.oon = Math.min(res.medical.single.oon || Infinity, res.medical.family.oon || Infinity);

    res.drug.single.in_network = Math.min(res.drug.single.in_network || sMax, res.drug.family.in_network);
    res.drug.single.in_network2 = Math.min(res.drug.single.in_network2 || sMax, res.drug.family.in_network2 || sMax);
    res.drug.single.oon = Math.min(res.drug.single.oon || Infinity, res.drug.family.oon || Infinity);

    return res;
};

// Function to parse benefits from the CSV row
const parseBenefitsFromRow = (row: any) => {
    const coins: any = defBen();
    const copay: any = defBen();
    const benefits: any = {};

    // Add basic benefits based on SBC data
    const addBenefit = (name: string, covered: boolean, detail: string, category: string, networkTier: string, coinsRate?: number, copayAmount?: number) => {
        benefits[name] = {
            label: name,
            covered,
            detail: detail || ''
        };

        const networkKey = getNetworkKey(networkTier);

        if (category && networkKey) {
            if (coinsRate) {
                coins[networkKey].display.push(`${name}|coinsurance|${coinsRate}%`);
                coins[networkKey].categories[category] = coinsRate / 100; // Convert percentage to decimal
            }

            if (copayAmount) {
                copay[networkKey].display.push(`${name}|copay|$${copayAmount}`);
                copay[networkKey].categories[category] = copayAmount;
            }
        }
    };

    // Parse SBC Having a Baby data
    if (row['SBC HAVING A BABY COINSURANCE']) {
        const coinsRate = parseFloat(row['SBC HAVING A BABY COINSURANCE']?.replace(/[^0-9.]/g, '') || 0);
        const copayAmount = row['SBC HAVING A BABY COPAYMENT'] ? parseFloat(row['SBC HAVING A BABY COPAYMENT'].replace(/[^0-9.]/g, '') || 0) : undefined;

        addBenefit(
            'Maternity Care',
            true,
            `Deductible: ${row['SBC HAVING A BABY DEDUCTIBLE'] || '$0'}, Copay: ${row['SBC HAVING A BABY COPAYMENT'] || '$0'}, Coinsurance: ${row['SBC HAVING A BABY COINSURANCE'] || '0%'}`,
            'specialist',
            'In-Network',
            coinsRate,
            copayAmount
        );
    }

    // Parse SBC Diabetes data
    if (row['SBC HAVING DIABETES COINSURANCE']) {
        const coinsRate = parseFloat(row['SBC HAVING DIABETES COINSURANCE']);
        const copayAmount = row['SBC HAVING DIABETES COPAYMENT'] ? parseFloat(row['SBC HAVING DIABETES COPAYMENT'].replace(/[^0-9.]/g, '')) : undefined;

        addBenefit(
            'Diabetes Care',
            true,
            `Deductible: ${row['SBC HAVING DIABETES DEDUCTIBLE'] || '$0'}, Copay: ${row['SBC HAVING DIABETES COPAYMENT'] || '$0'}, Coinsurance: ${row['SBC HAVING DIABETES COINSURANCE'] || '0%'}`,
            'primary_care',
            'In-Network',
            coinsRate,
            copayAmount
        );
    }

    // Parse SBC Simple Fracture data
    if (row['SBC HAVING SIMPLE FRACTURE COINSURANCE']) {
        const coinsRate = parseFloat(row['SBC HAVING SIMPLE FRACTURE COINSURANCE']);
        const copayAmount = row['SBC HAVING SIMPLE FRACTURE COPAYMENT'] ? parseFloat(row['SBC HAVING SIMPLE FRACTURE COPAYMENT'].replace(/[^0-9.]/g, '')) : undefined;

        addBenefit(
            'Emergency Care',
            true,
            `Deductible: ${row['SBC HAVING SIMPLE FRACTURE DEDUCTIBLE'] || '$0'}, Copay: ${row['SBC HAVING SIMPLE FRACTURE COPAYMENT'] || '$0'}, Coinsurance: ${row['SBC HAVING SIMPLE FRACTURE COINSURANCE'] || '0%'}`,
            'emergency_room',
            'In-Network',
            coinsRate,
            copayAmount
        );
    }

    // Calculate averages
    for (const k in coins) {
        coins[k].avg = blendCoins(coins[k].categories);
        copay[k].avg = blendCoins(copay[k].categories);
    }

    return {coins, copay, benefits};
};

// Main function to normalize a row from the CSV into a NormPolicy
export const normalizeStateExchangePolicy = (row: any): NormPolicy => {
    const {coins, copay, benefits} = parseBenefitsFromRow(row);
    const {title, subtitle} = getPolicyNames({name: row['PLAN MARKETING NAME']});

    // Parse premium (not available in the sample data, so using a placeholder)

    return {
        _id: '',
        plan_id: row['STANDARD COMPONENT ID'],
        acaPlan: true,
        type: 'aca',
        name: row['PLAN MARKETING NAME'],
        title,
        subtitle,
        business_year: Number(row['BUSINESS YEAR']),
        state_code: row['STATE CODE'].toUpperCase(),
        metal: getMetal(row['METAL LEVEL']),
        issuer_id: row['ISSUER ID'] || '',
        carrierName: row['ISSUER NAME'] || '',
        carrierLogo: '',
        plan_type: row['PLAN TYPE'],
        aptc_eligible_premium: undefined, // Not available in the sample data
        off_exchange: !row['QHP NONQHP TYPE ID']?.toLowerCase().includes('exchange'),
        on_exchange: !!row['QHP NONQHP TYPE ID']?.toLowerCase().includes('exchange'),
        benefits,
        copay,
        coins,
        moop: parseMoopOrDeductible(row, 'MOOP'),
        deductible: parseMoopOrDeductible(row, 'DED'),
        hsa_eligible: row['IS HSA ELIGIBLE']?.toLowerCase() === 'yes',
        eligible_dependents: [], // Not available in the sample data
        benefits_url: row['URL FOR SUMMARY OF BENEFITS COVERAGE'] || '',
        brochure_url: row['PLAN BROCHURE'] || '',
        formulary_url: row['FORMULARY URL'] || '',
        network_url: '', // Not available in the sample data
        tobacco_lookback: undefined, // Not available in the sample data
        exclusions: row['PLAN LEVEL EXCLUSIONS'] || ''
    };
};

// Function to read and parse an Excel file
export const uploadSePlans = ({csvData, year}: any) => {
    return async (context: HookContext) => {
        // Extract headers (first row)
        const errs: Array<{ id: string, message: string }> = [];
        const byId:any = {};
        const state_plan_ids:any = [];
        // Convert the data to an array of objects with headers as keys
        const rows: any = [];
        for (let i = 1; i < csvData.length; i++) {
            const row = csvData[i];
            if (!row || row.length === 0) continue; // Skip empty rows

            const headerKeys = Object.keys(headers);
            const rowObj: any = {};
            for (let j = 0; j < headerKeys.length; j++) {
                if (j < row.length) {
                    rowObj[headerKeys[j]] = row[j] || '';
                } else row[j] = ''
            }
            rows.push(rowObj);
        }

        const jd = await new CoreCall('junk-drawers', context).find({
            query: {
                $limit: 1,
                itemId: `issuers|ids`
            }
        })

        const issuerById = jd.data[0].data[rows[0]['STATE CODE']]

        // Process each row into a normalized policy
        for (const rowObj of rows) {
            // Skip rows without essential data
            if (!rowObj['PLAN ID']) continue;
            if(rowObj['MARKET COVERAGE']?.toLowerCase() !== 'individual') continue;
            if(rowObj['DENTAL ONLY PLAN']?.toLowerCase() === 'yes') continue;
            try {
                if(year) rowObj['BUSINESS YEAR'] = year;
                const {_id, ...policy} = normalizeStateExchangePolicy(rowObj);
                if(!policy.carrierName) policy.carrierName = issuerById[policy.issuer_id as string]
                for(const k in policy){
                    if(!policy[k]) delete policy[k]
                }
                const sid = rowObj['PLAN ID'].split('-')[1];
                /** see which CSR variants are present and nest them on the standard plan - only supporting silver CSR plans at this time in the workflow, but nesting all of them because it would be painful to go back and do it later */
                if(sid !== '01') {
                    if(byId[policy.plan_id]){
                        byId[policy.plan_id].csr = { ...byId[policy.plan_id].csr, [sid]: policy }
                    }
                } else byId[policy.plan_id] = policy;
                state_plan_ids.push(`${policy.state_code?.toLowerCase()}:${policy.business_year}:${rowObj['STANDARD COMPONENT ID']}`)
            } catch (error: any) {
                console.error('Error normalizing policy:', error);
                errs.push({id: rowObj['STANDARD COMPONENT ID'], message: error.message})
                // Continue with other rows
            }
        }

        const ex = await new CoreCall('se-plans', context).find({
            query: {
                $limit: state_plan_ids.length,
                state_plan_id: {$in: state_plan_ids}
            }
        })

        if(!ex) throw new Error(`Failed to search existing plans - cannot add with risk of duplication`)

        const promises:any = []
        for(let i = 0; i < ex.data.length; i++) {
            promises.push(new CoreCall('se-plans', context).patch(ex.data[i]._id, byId[ex.data[i].plan_id])
                .catch(err => errs.push({id: ex.data[i].plan_id, message: err.message})))
            delete byId[ex.data[i].plan_id]

        }

        const patched = promises.length ? await Promise.all(promises) : []
        let create:any = [];
        for(const k in byId) {
            create.push(new CoreCall('se-plans', context).create(byId[k])
                .catch(err => errs.push({id: byId[k].plan_id, message: err.message})))
        }
        const created = await Promise.all(create)
        context.result = { data: [...patched, ...created], errs };

        return context;
    }
};
