
// TypeBox schema for se-plans service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const sePlansSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: Type.Optional(ObjectIdSchema()),
  public: Type.Optional(Type.Boolean()),
  template: Type.Optional(Type.Boolean()),
  fromTemplate: Type.Optional(ObjectIdSchema()),
  sim: Type.Optional(Type.Boolean()),
  rating_areas: Type.Optional(
      Type.Record(Type.String(), Type.Object({
        name: Type.Optional(Type.String()),
        zips: Type.Optional(Type.Array(Type.String())),
        fips: Type.Optional(Type.Array(Type.String())),
        cities: Type.Optional(Type.Array(Type.String())),
        county: Type.Optional(Type.String()),
        rates: Type.Optional(Type.Record(Type.String(), Type.Number()))
      }))
  ),
  rateIncrease: Type.Optional(Type.Object({
    amount: Type.Optional(Type.Number()),
    date: Type.Optional(Type.Any())
  })),
  all_fips: Type.Optional(Type.Array(Type.String())),
  first_3_zips: Type.Optional(Type.Array(Type.String())),
  all_zips: Type.Optional(Type.Array(Type.String())),
  fortyPremium: Type.Optional(Type.Number()),
  design: Type.Optional(Type.String()),
  ...normPolicySchema.properties,
  csr: Type.Optional(Type.Record(Type.String(), Type.Any())),
  ...commonFields.properties
}, {  additionalProperties: false })

export type SePlans = Static<typeof sePlansSchema>
export const sePlansValidator = getValidator(sePlansSchema, dataValidator)
export const sePlansResolver = resolve<SePlans, HookContext>({})
export const sePlansExternalResolver = resolve<SePlans, HookContext>({})

export const sePlansDataSchema = Type.Object({
  ...Type.Omit(sePlansSchema, ['_id']).properties
}, { additionalProperties: false })

// Pick ObjectId fields and common meta ObjectId fields for query properties
const sePlansQueryProperties = Type.Pick(sePlansSchema, ['_id', 'org', 'fromTemplate', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])
export type SePlansData = Static<typeof sePlansDataSchema>
export const sePlansDataValidator = getValidator(sePlansDataSchema, dataValidator)
export const sePlansDataResolver = resolve<SePlansData, HookContext>({})


export const sePlansPatchSchema = commonPatch(sePlansSchema, { pushPullOpts: [], pickedForSet: sePlansQueryProperties })
export type SePlansPatch = Static<typeof sePlansPatchSchema>
export const sePlansPatchValidator = getValidator(sePlansPatchSchema, dataValidator)
export const sePlansPatchResolver = resolve<SePlansPatch, HookContext>({})

export const sePlansQuerySchema = queryWrapper(sePlansQueryProperties)
export type SePlansQuery = Static<typeof sePlansQuerySchema>
export const sePlansQueryValidator = getValidator(sePlansQuerySchema, queryValidator)
export const sePlansQueryResolver = resolve<SePlansQuery, HookContext>({})
