// TypeBox schema for caps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const capsSchema = Type.Object({
  _id: ObjectIdSchema(),
  subject: ObjectIdSchema(),
  subjectService: Type.Optional(Type.String()),
  did: Type.String(),
  caps: Type.Optional(Type.Record(Type.String(), Type.Object({
    description: Type.Optional(Type.String()),
    ucan: Type.Optional(Type.String()),
    logins: Type.Optional(Type.Array(ObjectIdSchema())),
  }))),
  ...commonFields.properties
}, { additionalProperties: false })

export type Caps = Static<typeof capsSchema>
export const capsValidator = getValidator(capsSchema, dataValidator)
export const capsResolver = resolve<Caps, HookContext>({})
export const capsExternalResolver = resolve<Caps, HookContext>({})

export const capsDataSchema = Type.Object({
  ...Type.Omit(capsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CapsData = Static<typeof capsDataSchema>
export const capsDataValidator = getValidator(capsDataSchema, dataValidator)
export const capsDataResolver = resolve<CapsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const capsQueryProperties = Type.Pick(capsSchema, ['_id', 'subject', 'caps'], { additionalProperties: true })

export const capsPatchSchema = commonPatch(capsSchema, {pushPullOpts: [], pickedForSet: capsQueryProperties})
export type CapsPatch = Static<typeof capsPatchSchema>
export const capsPatchValidator = getValidator(capsPatchSchema, dataValidator)
export const capsPatchResolver = resolve<CapsPatch, HookContext>({})
export const capsQuerySchema = queryWrapper(capsQueryProperties, {}, { opMap: { caps: ['$in'], subject: ['$in'] } })
export type CapsQuery = Static<typeof capsQuerySchema>
export const capsQueryValidator = getValidator(capsQuerySchema, queryValidator)
export const capsQueryResolver = resolve<CapsQuery, HookContext>({})
