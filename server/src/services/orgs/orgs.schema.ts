// TypeBox schema for orgs service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    commonFields,
    phoneSchema,
    imageSchema,
    addressSchema,
    queryWrapper,
    commonPatch, opProp
} from '../../utils/common/typebox-schemas.js'

/** payrollSettings */
export const payrollSettings = Type.Object(
    {
        frequency: Type.Optional(
            Type.String({
                enum: ['daily', 'weekly', 'bi-weekly', 'semi-monthly', 'monthly', 'quarterly', 'annually', 'int'],
            })
        ),
    },
    {additionalProperties: true}
);

/** ownSchema */
export const ownSchema = Type.Object(
    {
        did: Type.Optional(Type.String()),
        percent: Type.Optional(Type.Number()),
        id: Type.Optional(ObjectIdSchema()), // ppls
        attribute: Type.Optional(Type.Array(ObjectIdSchema())),
        executive: Type.Optional(Type.Boolean()),
        director: Type.Optional(Type.Boolean()),
        position: Type.Optional(Type.String()), // e.g., shareholder, member, board...
    },
    {additionalProperties: false}
);

/** orgsSchema */
export const orgsSchema = Type.Object(
    {
        // required
        _id: ObjectIdSchema(),
        name: Type.String(),

        // optional
        bankAccounts: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        name: Type.Optional(Type.String()),
                        id: Type.Optional(ObjectIdSchema()),
                        default: Type.Optional(Type.Boolean()),
                    },
                    {additionalProperties: true}
                )
            )
        ),

        address: Type.Optional(addressSchema),
        addresses: Type.Optional(Type.Array(addressSchema)),
        affiliatedOrgs: Type.Optional(Type.Array(ObjectIdSchema())),

        asg: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        type: Type.Optional(Type.String({enum: ['A', 'B']})),
                        orgs: Type.Optional(
                            Type.Record(
                                Type.String(),
                                Type.Object(
                                    {
                                        type: Type.Optional(Type.String({enum: ['A', 'B', 'FSO', 'M']})),
                                    },
                                    {additionalProperties: true}
                                )
                            )
                        ),
                    },
                    {additionalProperties: true}
                )
            )
        ),

        avatar: Type.Optional(imageSchema),
        budgets: Type.Optional(Type.Array(ObjectIdSchema())),

        controls: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        identical: Type.Optional(Type.Number()),
                        common: Type.Optional(Type.Number()),
                        control: Type.Optional(Type.Boolean()),
                        orgs: Type.Optional(
                            Type.Record(
                                Type.String(),
                                Type.Object(
                                    {
                                        identical: Type.Optional(Type.Number()),
                                        owners: Type.Optional(Type.Array(ObjectIdSchema())),
                                        parent: Type.Optional(Type.Any()),
                                        total: Type.Optional(Type.Number()),
                                    },
                                    {additionalProperties: true}
                                )
                            )
                        ),
                        brotherSister: Type.Optional(Type.Boolean()),
                        parentSub: Type.Optional(Type.Boolean()),
                    },
                    {additionalProperties: true}
                )
            )
        ),

        customers: Type.Optional(Type.Array(ObjectIdSchema())),
        coverImage: Type.Optional(imageSchema),

        did: Type.Optional(Type.String()),
        ein: Type.Optional(Type.String()),
        email: Type.Optional(Type.String()),
        emails: Type.Optional(Type.Array(Type.String())),

        hostAccounts: Type.Optional(Type.Array(ObjectIdSchema())),
        industries: Type.Optional(Type.Array(Type.String())),
        images: Type.Optional(Type.Array(imageSchema)),

        groups: Type.Optional(
            Type.Record(Type.String(), ObjectIdSchema())
        ),
        groupIds: Type.Optional(Type.Array(ObjectIdSchema())), // same as groups but for querying

        legalName: Type.Optional(Type.String()),
        managementOrgs: Type.Optional(Type.Array(ObjectIdSchema())),
        plans: Type.Optional(Type.Array(ObjectIdSchema())),
        providerAccounts: Type.Optional(Type.Array(ObjectIdSchema())),

        owners: Type.Optional(
            Type.Array(
                Type.Object(
                    {
                        ...ownSchema.properties, // properties already optional & additionalProperties:false
                        idService: Type.Optional(Type.String()),
                        name: Type.Optional(Type.String()),
                        email: Type.Optional(Type.String()),
                        phone: Type.Optional(phoneSchema),
                        address: Type.Optional(addressSchema),
                    },
                    {additionalProperties: false}
                )
            )
        ),

        ownerSync: Type.Optional(Type.Boolean()),
        phone: Type.Optional(phoneSchema),
        phones: Type.Optional(Type.Array(phoneSchema)),
        payroll_settings: Type.Optional(payrollSettings),
        public: Type.Optional(Type.Boolean()),
        ramp_vendor_id: Type.Optional(Type.String()),
        refs: Type.Optional(Type.Array(ObjectIdSchema())),
        singleMember: Type.Optional(Type.Boolean()),
        memberCount: Type.Optional(Type.Number()),
        memberCountAt: Type.Optional(Type.Any()),

        structure: Type.Optional(
            Type.String({
                enum: ['PARTNERSHIP', 'SOLE_PROPRIETOR', 'NONPROFIT', 'CORPORATION', 'S-CORP', 'LLC', 'LLP', 'OTHER'],
            })
        ),

        taxes: Type.Optional(
            Type.Object(
                {
                    incomeTaxForm: Type.Optional(Type.String()), // e.g. 990, 1040, 1120, 1120S, 1065
                },
                {additionalProperties: true}
            )
        ),

        taxStructure: Type.Optional(Type.String()),
        threads: Type.Optional(Type.Array(ObjectIdSchema())),
        careAccounts: Type.Optional(Type.Array(ObjectIdSchema())),

        treasury: Type.Optional(
            Type.Object(
                {
                    customerId: Type.Optional(Type.String()),
                    id: Type.Optional(Type.String()), // account id
                    business_profile: Type.Optional(
                        Type.Object(
                            {
                                support_email: Type.Optional(Type.String()),
                                annual_revenue: Type.Optional(
                                    Type.Object(
                                        {
                                            amount: Type.Optional(Type.Number()), // cents
                                            currency: Type.Optional(Type.String()),
                                            fiscal_year_end: Type.Optional(Type.String()),
                                        },
                                        {additionalProperties: true}
                                    )
                                ),
                                estimated_worker_count: Type.Optional(Type.Number()),
                                mcc: Type.Optional(Type.String()),
                                mcc_name: Type.Optional(Type.String()),
                                naics: Type.Optional(Type.String()),
                                sic: Type.Optional(Type.String()),
                                product_description: Type.Optional(Type.String()),
                                support_address: Type.Optional(addressSchema),
                                support_url: Type.Optional(Type.String()),
                                url: Type.Optional(Type.String()),
                                representatives: Type.Optional(
                                    Type.Array(
                                        Type.Object(
                                            {
                                                isController: Type.Optional(Type.Boolean()),
                                            },
                                            {additionalProperties: true}
                                        )
                                    )
                                ),
                            },
                            {additionalProperties: true}
                        )
                    ),
                },
                {additionalProperties: true}
            )
        ),

        website: Type.Optional(Type.String()),

        // common fields (keep their own optional/required semantics)
        ...commonFields.properties,
    },
    {
        additionalProperties: true,
        required: ['_id', 'name'],
    }
);

export type Orgs = Static<typeof orgsSchema>
export const orgsValidator = getValidator(orgsSchema, dataValidator)
export const orgsResolver = resolve<Orgs, HookContext>({})
export const orgsExternalResolver = resolve<Orgs, HookContext>({})

// Schema for creating new data
export const orgsDataSchema = Type.Object({
    ...Type.Omit(orgsSchema, ['_id']).properties
}, {additionalProperties: false})

export type OrgsData = Static<typeof orgsDataSchema>
export const orgsDataValidator = getValidator(orgsDataSchema, dataValidator)
export const orgsDataResolver = resolve<OrgsData, HookContext>({})

const OwnersElemMatch = Type.Object(
    {$elemMatch: Type.Object({id: ObjectIdSchema()}, {additionalProperties: true})},
    {additionalProperties: true}
);

const OwnersSchema = (orgsSchema as any).properties.owners;

const idPickFields = ['_id', 'affiliatedOrgs', 'groups', 'controls', 'groupIds', 'owns.id']
export const orgsQueryProperties = Type.Intersect([
    Type.Pick(orgsSchema, idPickFields),
    Type.Object({
        owners: Type.Union([OwnersElemMatch, OwnersSchema])
    }, {additionalProperties: true})
]);

// Schema for updating existing data
export const orgsPatchSchema = commonPatch(orgsSchema, {pushPullOpts: [], pickedForSet: orgsQueryProperties})

export type OrgsPatch = Static<typeof orgsPatchSchema>
export const orgsPatchValidator = getValidator(orgsPatchSchema, dataValidator)
export const orgsPatchResolver = resolve<OrgsPatch, HookContext>({})

const orType = Type.Array(Type.Object({
    affiliatedOrgs: opProp(Type.Array(ObjectIdSchema()), ['$in']),
    _id: opProp(ObjectIdSchema(), ['$in'])
}, {additionalProperties: true}))

const opMap = Object.fromEntries(idPickFields.map(([k, v]) => [k, ['$in']]))
export const orgsQuerySchema = queryWrapper(orgsQueryProperties, {
    owners: Type.Union([orgsSchema.properties.owners, Type.Object({
        $elemMatch: Type.Any()
    }, {additionalProperties: true})]),
    'owners.id': ObjectIdSchema(),
    name: Type.Any(),
    email: Type.Any(),
    phone: Type.Any(),
    _owners: Type.Any(),
    _attribute: Type.Any(),
    $or: orType,
    $and: orType
}, {opMap})

export type OrgsQuery = Static<typeof orgsQuerySchema>
export const orgsQueryValidator = getValidator(orgsQuerySchema, queryValidator)
export const orgsQueryResolver = resolve<OrgsQuery, HookContext>({})
