import {CoreCall} from 'feathers-ucan';
import {_get, _set} from 'symbol-ucan';
import {HookContext} from '../../../declarations.js';
import {findJoin} from '../../../../utils/fast-join.js';


export const getOwners = (ownerType: string): (c: HookContext) => Promise<HookContext> => {
    return async (context) => {
        // console.log('get owners', context.params.runJoin);
        if(context.params.skip_hooks || context.params[`gettingOwners${ownerType}`]) return context;
        context.params[`gettingOwners${ownerType}`] = true;
        if (context.params.runJoin?.owners) {
            const queryFn = (item) => {
                let ownerList = [];
                if (item.owners) {
                    ownerList = item.owners.filter(a => a.id && a.idService === ownerType).map(a => a.id);
                }
                return {
                    _id: {$in: ownerList}
                };
            };

            return findJoin({
                service: ownerType,
                queryFn,
                therePath: 'owns',
                joinPath: ['owners', ownerType],
                herePath: '_id'
            })(context);
        } else return context;
    };
};

const relateOwners = async context => {

    if (!context.params.$relatingOwners) {
        if (context.type === 'after') {
            context.params.$relatingOwners = true;
        }
        if (context.method === 'remove') {
            const removeFn = async owner => {
                return await new CoreCall(owner.idService, context, {skipJoins: true}).patch(owner.id, {$pull: {owns: {id: context.result._id}}});
            };

            await Promise.all((_get(context.result, 'owners', []) || []).map(a => {
                return removeFn(a);
            }));
            return context;

        } else {
            if (context.type === 'before') {
                if (context.id) {
                    const existing = await new CoreCall('orgs', context, {skipJoins: true}).get(context.id)
                        .catch(err => {
                            console.log('error getting prior orgs', err.message);
                            return null;
                        });
                    context.params.$priorOwners = existing?.owners;
                } else return context;
            } else {
                let {$priorOwners} = context.params;

                let priorIds = $priorOwners ? $priorOwners.map(po => po.id) : [];
                let diff = (_get(context.result, 'owners', []) || [{id: null}]).filter(a => !$priorOwners || !priorIds.includes(a.id));

                let ownerIds = (_get(context.result, 'owners', []) || [{id: null}]).map(a => a.id);

                if ($priorOwners) $priorOwners.forEach(a => {
                    if (!ownerIds.includes(a.id)) {
                        diff.push(a);
                    }
                });
                if (diff && diff.length) {

                    let relateOwner = async owner => {
                        let idx = $priorOwners.map(a => String(a.id)).indexOf(String(owner.id));

                        let patchObj = {id: context.result._id, percent: owner.percent, position: owner.position};
                        if (_get($priorOwners, [idx, 'percent']) !== owner.percent) {
                            const newOwners = await new CoreCall(owner.idService, context, {skipJoins: true}).patch(null, {$addToSet: {owns: patchObj}}, {
                                query: {
                                    _id: owner.id,
                                    'owns.id': {$nin: [context.result._id]}
                                }
                            })
                                .then(res => {
                                    console.log('1st res', res);
                                })
                                .catch(err => {
                                    console.log('1st err', err);
                                });

                            const updatedOwners = await new CoreCall(owner.idService, context, {skipJoins: true}).patch(null, {
                                $set: {
                                    'owns.$.id': patchObj.id,
                                    'owns.$.percent': patchObj.percent
                                }
                            }, {
                                query: {
                                    _id: owner.id,
                                    'owns.id': {$in: [context.result._id]}
                                }
                            })
                                .then(res => {
                                    console.log('2nd res', res);
                                })
                                .catch(err => {
                                    console.log('2nd err', err);
                                });


                            _set(context.params, ['updatedOwners', idx], updatedOwners);
                            _set(context.params, ['newOwners', idx], newOwners);
                        }
                    };
                    context.params.updatedOwnerList = await Promise.all(diff.map(a => relateOwner(a)));
                }
            }
            return context;

        }
    } else {
        return context;
    }
};

const getOwns = () => {
    return async context => {
        if (context.params.runJoin?.owns) {
            const queryFn = (item) => {
                let ownsList = [];
                if (item.owns) {
                    ownsList = item.owns.filter(a => a.id && a.idService === ownsList).map(a => a.id);
                }
                return {
                    _id: {$in: ownsList}
                };
            };

            return findJoin({
                service: 'orgs',
                queryFn,
                therePath: '_id',
                joinPath: ['owns'],
                herePath: '_id'
            })(context);
        } else return context;
    };
};
