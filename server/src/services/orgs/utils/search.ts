import {getControls, getCapTable} from './attribution.js';
import {HookContext} from '../../../declarations.js';
import {_set} from '../../../../utils/dash-utils.js';
import {AnyObj} from '../../../../utils/types.js';
import {CoreCall} from 'feathers-ucan';

const assembleControl = async (org: any, context: HookContext): Promise<any> => {
    if (org && context) {
        const query = {
            $limit: 100,
            '_owners': {
                ids: (org.owners || []).map(a => a.id)
            }
        }
        const orgs = await new CoreCall('orgs', context).find({query})
        const capTable = getCapTable(orgs.data);
        const controls = getControls(capTable.orgs, capTable.management);
        org._fastjoin = {...org._fastjoin || {}, capTable, controls};

        const affOrgs: Array<string> = [];
        if (org.ownerSync) {
            const {controls: rControls} = org;
            if (!rControls) org.controls = {};
            for (const k in org.controls) {
                if (!rControls[k].asg && !controls.ctrls[k]) delete org.controls[k];
            }
            for (const k in controls.ctrls) {
                const existing = (rControls || {[k]: {}})[k] || {};
                const generated = controls.ctrls[k] || {};
                const newCtrl = {
                    ...existing,
                    ...generated,
                    // orgs: {
                    //     ...existing.orgs,
                    //     ...generated.orgs
                    // }
                };
                org.controls[k] = newCtrl;
                for (const orgK in newCtrl.orgs || {}) {
                    affOrgs.push(orgK);
                }
            }
            org = await new CoreCall('orgs', context).patch(context.id as any, {
                controls: org.controls,
                ownerSync: false,
                affiliatedOrgs: Array.from(new Set(affOrgs))
            }, {admin_pass: true})
            return org
        } else return org;
    } else return undefined
}
export const attributeOwnership = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    const {_attribute} = context.params;
    if (_attribute) {
        context.params._attribute = false;
        context.result = await assembleControl(context.result, context);
        if(context.result.ownerSync) {
            await Promise.all((context.result?._fastjoin?.capTable?.orgRecords || []).map(a => assembleControl({...a, ownerSync: true}, context)))
        }
    }

    return context;
}

export const searchMembers = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;
    if (context.method === 'find') {
        const {query} = context.params;

        if (context.type === 'before' && query?.search_members) {

            // const memberQuery:AnyObj = { members: { $in: query.search_members.map(a => ObjectId.createFromHexString(a) ) }};
            const groupQuery: AnyObj = {members: {$in: query.search_members}};

            if (query._id) groupQuery.org = query._id;

            const groups = await new CoreCall('groups', context, {skipJoins: true}).find({query: groupQuery});

            if (!groups.total) return context;
            if (groups.data.length === 1) query['_id'] = groups.data[0].org;

            const uniqueIds: any[] = [];
            const stringIds: string[] = [];
            for (const group of groups.data || []) {
                const id = String(group.org);
                if (!stringIds.includes(id)) {
                    stringIds.push(id);
                    uniqueIds.push(group.org)
                }
            }
            query._id = {$in: uniqueIds};

            delete query.search_members;
            context.params.query = query;
            context.params.search_groups = groups;

        } else if (context.type === 'after' && context.params.search_groups) {
            context.result.data.forEach(org => org._fastjoin = _set(org._fastjoin, 'groups', context.params.search_groups.data.filter(a => a.org === org._id)))
        }
    }
    return context;
};
