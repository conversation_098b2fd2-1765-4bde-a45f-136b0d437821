import {hooks as schemaHooks} from "@feathersjs/schema";
import {
    orgsDataResolver,
    orgsDataValidator,
    orgsExternalResolver,
    orgsPatchResolver,
    orgsPatchValidator,
    orgsQueryResolver,
    orgsQueryValidator,
    orgsResolver,
    orgsSchema
} from "../orgs.schema.js";

import {HookContext} from "../../../declarations.js";
import {logChange, logHistory} from '../../../utils/change-log.js';
import {scrub} from '../../../utils/sanitize/index.js';
import {scrubUploads} from '../../../utils/file-join.js';
import {trimIt, singleEmail, multiEmail} from '../../../utils/schemas/index.js';
import {encryptedFields} from '../../../utils/encryption/index.js';
import {_pick} from '../../../utils/dash-utils.js';

import {searchMembers, attributeOwnership} from '../utils/search.js';

import {allUcanAuth, anyAuth, CapabilityParts, ucanAuth, loadExists, setExists, CoreCall} from 'feathers-ucan';


import {getOwners} from '../utils/relate-owners.js'


/*paths are [single, plural] such as ['email', 'emails']*/
export const addContacts = (paths: string[][]) => {
    return (context: HookContext) => {
        const getAddToSet = (p: string) => {
            const single = (context.data.$addToSet || {})[p]
            if (single) {
                if (single.$each) return single.$each;
                else return [single];
            }
            return []
        }
        const addContact = (s: string, p: string) => {
            const plural = (context.data[p] || getAddToSet(p))[0];
            const single = context.data[s] || (context.data.$set || {})[s]
            if (plural && !single) context.data[s] = plural;
            if (single && !plural) context.method === 'create' ? context.data[p] = [single] : context.data.$addToSet = {
                ...context.data.$addToSet,
                [p]: single
            }
        };
        for (let i = 0; i < paths.length; i++) {
            addContact(paths[i][0], paths[i][1])
        }
        return context;
    }
}
const contactPaths = [['email', 'emails'], ['phone', 'phones'], ['address', 'addresses']];


const capOwnership = context => {
    const addPercent = (acc, curr) => acc.percent || 0 + curr;

    const percent = context.data.owners ? context.data.owners.reduce(addPercent, 0) : 0;
    if (percent > 100) throw new Error('Cannot have more than 100% ownership');
    else return context;
};


import {ObjectId} from 'mongodb';


const authenticate = async (context: HookContext): Promise<HookContext> => {
    const creator = [['orgs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['orgs', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        create: anyAuth,
        patch: [
            ['orgs', 'WRITE'],
            [`orgs:${context.id}`, 'WRITE']
        ],
        update: creator,
        remove: deleter
    }
    const cap_subjects: any = []
    if (context.id) {
        cap_subjects.push(context.id)
        const orgNamespace = `orgs:${context.id}`;
        const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']]
        for (const w of orgWrite) {
            // ucanArgs.create.unshift(w);
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }
    }
    // if(context.method === 'patch'){
    //
    //     const admin = ['name', 'email', 'emails', 'phone', 'phones', 'address', 'addresses', 'industries', 'structure', 'taxes', 'avatar', 'refs', 'owns', 'owners', 'members', 'plans', 'threads'];
    //     const plan_admin = ['plans', 'refs'];
    //
    // } else return await allUcanAuth<HookContext>(ucanArgs(context), {or: ['patch'], adminPass: ['remove']})(context);
    return await allUcanAuth<HookContext>(ucanArgs, {
        or: ['patch'],
        specialChange: context.params.special_change,
        loginPass: [[['createdBy.login'], ['patch']]],
        adminPass: ['remove', 'patch'],
        cap_subjects
    })(context);
}

const ownerSearch = async (context) => {
    if (context.params.skip_hooks) return context;
    const {query} = context.params;
    if (query?._owners) {
        const convertId = (id) => {
            if (typeof id === 'string' && id.length === 24) return ObjectId.createFromHexString(id)
            else return id;
        }
        if (query._owners.ids) {
            const list = query._owners.ids.map(a => convertId(a));
            query.$or ? query.$or.push({'owners.id': {$in: list}}) : query['owners.id'] = {$in: list};
        } else if (query._owners.id) {
            query.$or ? query.$or.push({'owners.id': convertId(query._owners.id)}) : query['owners.id'] = convertId(query._owners.id);
        }
        delete query._owners;
    }
}

const imagePaths = ['avatar', 'images', 'coverImage']

const format = (context: HookContext): HookContext => {
    const data = context.data;
    if (Array.isArray(data)) return context;
    const obj = {
        'name': (val) => trimIt(val),
        'ein': (val) => val.split('-').join(''),
        'email': (val) => singleEmail(val),
        'emails': (val) => multiEmail(val)
    }
    Object.keys(obj).forEach(key => {
        if (data[key]) data[key] = obj[key](data[key]);
    })
    return context;
}


const checkId = (id: any) => {
    if (typeof id === 'string' && id.length === 24) return ObjectId.createFromHexString(id);
    else return id;
}
const logOwnerChange = async (context: HookContext) => {
    if (Array.isArray(context.data)) return context;
    const exists = await loadExists(context);
    context = setExists(context, exists);
    let change;
    if (context.data.$addToSet?.owners) {
        change = true;
        if (context.data.$addToSet.owners.$each) context.data.$addToSet.owners.$each.forEach(owner => owner.id = checkId(owner.id))
        else context.data.$addToSet.owners.id = checkId(context.data.$addToSet.owners.id)
    }
    if (context.data.$set?.owners) {
        change = true
        context.data.$set.owners = context.data.$set.owners.map(a => ({...a, id: checkId(a.id)}))
    }
    if (context.data.$unset?.owners) change = true;
    if (context.data.owners) {
        change = true;
        context.data.owners = context.data.owners.map(a => ({...a, id: checkId(a.id)}))
    }
    if (change) context.data.ownerSync = true

    return context;
}

/** can go around with _search */
const limitSearch = async (context: HookContext): Promise<HookContext> => {
    if (context.params.skip_hooks) return context;
    if (!context.params._limit_search_loop) {
        //if all permissions exist
        const ctx = await ucanAuth([['orgs', 'READ']], {noThrow: true})(context) as any;
        if (ctx.params._no_throw_error && !ctx.params.admin_pass) {
            //otherwise
            const {owner: person, _id} = ctx.params.login || {};
            if (person) {

                let id = context.params.query._id
                if (!id) {
                    const inOrgs = (person.inOrgs || []).filter(a => !!a).map(a => typeof a === 'string' ? ObjectId.createFromHexString(a) : a);

                    const q = {
                        $or: [
                            {_id: {$in: inOrgs}},
                            {affiliatedOrgs: {$in: inOrgs}},
                            {'updatedBy.login': _id}
                        ]
                    }
                    const keys = Object.keys(context.params.query || {})
                    const operators: any = {};
                    let runAnd = false;
                    const andObj = {};
                    for (let i = 0; i < keys.length; i++) {
                        if (keys[i].charAt(0) === '$') operators[keys[i]] = context.params.query[keys[i]]
                        else {
                            runAnd = true;
                            andObj[keys[i]] = context.params.query[keys[i]]
                        }
                    }
                    if (runAnd) context.params.query = {...operators, $and: [andObj, q]}
                    else context.params.query = {...operators, ...q}
                }

            } else if (!context.params._search || !context.params.query._id) throw new Error('No person associated with this login - cannot search orgs when not authenticated')
        }
    }
    return context;
}

const handleOwnerFormat = (context: HookContext) => {
    if (Array.isArray(context.data)) return context;
    if (context.data.owners || context.data.$addToSet?.owners) {
        const keys = Object.keys(orgsSchema.properties.owners.items.properties);
        const formatOwner = (v) => {
            return _pick(v, keys)
        }
        if (context.data.owners) context.data.owners = context.data.owners.map(formatOwner);
        if (context.data.$addToSet?.owners) {
            if (context.data.$addToSet.owners.$each) context.data.$addToSet.owners.$each = context.data.$addToSet.owners.$each.map(formatOwner);
            else context.data.$addToSet.owners = formatOwner(context.data.$addToSet.owners);
        }
    }
    return context;
}

const addCreator = async (context: HookContext): Promise<HookContext> => {
    if (context.params.login?.owner) {
        // Handle array results - add all created org IDs to the person's inOrgs
        if (Array.isArray(context.result)) {
            const orgIds = context.result.map(org => org._id);
            await new CoreCall('ppls', context).patch(context.params.login.owner, {
                $addToSet: {inOrgs: {$each: orgIds}}
            }, { admin_pass: true, skip_hooks: true });
        } else {
            await new CoreCall('ppls', context).patch(context.params.login.owner, {$addToSet: {inOrgs: context.result._id}}, { admin_pass: true, skip_hooks: true });
        }
    }
    return context;
}

const updateMemberCount = async (context: HookContext): Promise<HookContext> => {
    if (context.params.skip_hooks || context.params.update_member_count || Array.isArray(context.result)) return context;
    context.params.update_member_count = true;
    if (!context.result.memberCount || (new Date().getTime() - new Date(context.result.memberCountAt).getTime() > 1000 * 60 * 60)) {
        const allMembers = await new CoreCall('grp-mbrs', context, {skipJoins: true}).find({
            query: {
                $limit: 1,
                org: context.result._id
            },
            admin_pass: true,
            skip_hooks: true
        })
            .catch(err => console.error(`Error updating member count for group ${context.result._id}: ${err.message}`))
        if (allMembers?.total) {
            context.result = await new CoreCall('orgs', context).patch(context.result._id as any, {
                memberCount: allMembers.total,
                memberCountAt: new Date()
            })
                .catch(err => {
                    console.error(`Error updating member count for group ${context.result._id}: ${err.message}`)
                    return context.result;
                })
        }
    }
    return context;
}


export const orgHooks = {
    around: {
        all: [
            schemaHooks.resolveExternal(orgsExternalResolver),
            schemaHooks.resolveResult(orgsResolver)
        ]
    },
    before: {
        all: [
            authenticate,
            logChange(),
            searchMembers,
            schemaHooks.validateQuery(orgsQueryValidator),
            schemaHooks.resolveQuery(orgsQueryResolver),
            scrubUploads({paths: imagePaths}),
            encryptedFields(['ein'])
        ],
        find: [
            ownerSearch,
            limitSearch
        ],
        get: [],
        create: [
            // authenticate('jwt'),
            schemaHooks.validateData(orgsDataValidator),
            schemaHooks.resolveData(orgsDataResolver),
            format,
            capOwnership,
            addContacts(contactPaths),
            scrub(['info'])
            // relateOwners
        ],
        update: [
            // relateOwners,
        ],
        patch: [
            format,
            handleOwnerFormat,
            schemaHooks.validateData(orgsPatchValidator),
            schemaHooks.resolveData(orgsPatchResolver),
            logHistory(['name', 'email', 'phone', 'address', 'structure', 'owners', 'groups', 'geo']),
            capOwnership,
            addContacts(contactPaths),
            scrub(['info']),
            logOwnerChange
            // relateOwners,
        ],
        remove: []
    },

    after: {
        all: [
            scrubUploads({paths: imagePaths}),
            encryptedFields(['ein'])
        ],
        find: [
            searchMembers
        ],
        get: [
            getOwners('ppls'),
            getOwners('orgs'),
            attributeOwnership,
            updateMemberCount
            // getOwns()
        ],
        create: [
            addCreator,
            autoCreate('orgs', 'admins'),
            // relateOwners,
        ],
        update: [
            // relateOwners,
        ],
        patch: [
            updateMemberCount
            // relateOwners,
        ],
        remove: [
            // relateOwners,
        ]
    },

    error: {
        all: [],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: []
    }
};
