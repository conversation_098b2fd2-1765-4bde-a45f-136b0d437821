// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    groupsDataValidator,
    groupsPatchValidator,
    groupsQueryValidator,
    groupsResolver,
    groupsExternalResolver,
    groupsDataResolver,
    groupsPatchResolver,
    groupsQueryResolver
} from './groups.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {GroupsService, getOptions} from './groups.class.js'
import {groupsPath, groupsMethods} from './groups.shared.js'
import {memberUploads} from './utils/add-members.js';
import {
    CoreCall,
    allUcanAuth,
    getExists,
    loadExists,
    setExists,
    CapabilityParts
} from 'feathers-ucan';

import {getJoin} from '../../../utils/fast-join.js';
import {logChange, logHistory} from '../../../utils/change-log.js';

export * from './groups.class.js'
export * from './groups.schema.js'

export const groupKeyGen = (orgName: string, key: string): string => {
    return `${orgName}:${key}`.split(' ').join('_').toLowerCase();
};
const genKey = async (context: HookContext): Promise<HookContext> => {
    const existing = await loadExists(context)
        .catch(() => {
            return undefined
        });
    if (existing) context = setExists(context, existing);
    const setName = context.data.name || context.data.$set?.name;
    if (setName && (!existing || (existing && existing.name !== setName))) {
        const org = await new CoreCall('orgs', context, {skipJoins: true}).get(context.data.org as any);
        context.data.key = groupKeyGen(org.name, setName);
    }
    return context;
};


const authenticate = async (context: HookContext): Promise<HookContext> => {
    const writer = [['groups', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['groups', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: writer,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects: any = []
    if (!['get', 'find'].includes(context.method)) {
        const existing = await loadExists(context);
        context = setExists(context, existing);
        const orgId = context.data.org || existing?.org;
        if (orgId) {
            const orgNamespace = `orgs:${orgId}`;
            cap_subjects.push(orgId)
            const orgWrite: any = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'groupAdmin']] as CapabilityParts;
            for (const w of orgWrite) {
                ucanArgs.create.unshift(w);
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['create', 'remove', 'patch'],
        or: '*',
        cap_subjects
    })(context);
}

// export declare type UcanPayload<Prf = string> = {
//     iss: string;
//     aud: string;
//     exp: number;
//     nbf?: number;
//     nnc?: string;
//     att: Array<Capability>;
//     fct?: Array<Fact>;
//     prf: Array<Prf>;
// };

const removeMembers = async (context: HookContext): Promise<HookContext> => {
    await new CoreCall('ppls', context).patch(null, {$pull: {inGroups: context.id}}, {
        query: {inGroups: {$in: [context.result.id]}},
        admin_pass: true,
        skip_hooks: true
    })
        .catch(err => console.error(`Error removing group from people ${context.result._id}: ${err.message}`))
    await new CoreCall('grp-mbrs', context).remove(null, {
        query: {group: context.id},
        disableSoftDelete: true,
        admin_pass: true,
        skip_hooks: true
    })
        .catch(err => console.error(`Error removing group members ${context.result._id}: ${err.message}`))
    return context;
}


const handleOrgKeys = async (context: HookContext): Promise<HookContext> => {
    //needs to run after gen key
    if (context.type === 'before') {
        if (context.method !== 'create') {
            const em = await loadExists(context);
            context = setExists(context, em);
        }
    } else {
        const em: any = await loadExists(context);
        if (context.method === 'remove') {
            await new CoreCall('orgs', context, {skipJoins: true}).patch(em.org, {$unset: {[`groups.${em.key}`]: ''}}, {admin_pass: true})
        }
        const data = context.data
        const patchObj = {
            $set: {
                [`groups.${context.result.key}`]: context.result._id
            }
        }

        if ((context.result.key && context.result.key !== em?.key)) {
            if (em) {
                patchObj['$unset'] = {
                    [`groups.${em.key}`]: ''
                }
            }

            await new CoreCall('orgs', context, {skipJoins: true}).patch(context.result.org, patchObj, {admin_pass: true})
                .catch(async err => {
                    console.error(`Error updating org with new member key: ${context.result.key}:`, err.message);
                    // await new CoreCall('groups', context).remove(context.result._id, {admin_pass: true});
                    // throw new Error('Error adding groups to org');
                })
        }
    }
    return context;
}


const noOrgChange = async (context: HookContext) => {
    if (context.data.org) {
        const mr = await new CoreCall('groups', context, {skipJoins: true}).get(context.id as any)
        context = setExists(context, mr)
        if (String(mr.org) !== String(context.data.org))
            throw new Error('You cannot change the org of a member group')
        delete context.data.org
    }
    return context;
}


import multer from 'multer';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    req.feathers.file = req.file;
    req.feathers.addMembers = req.query.addMembers
    req.feathers.core = req.query.core
    return next();
}

const joinOrg = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    if (context.params.runJoin?.groupOrg) {
        return getJoin({service: 'orgs', herePath: 'org'})(context)
    }
    return context;
}



const updateMemberCount = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;
    const isArr = Array.isArray(context.result)
    const { get_member_count } = context.params.runJoin || {};
    if (context.params.update_member_count || (isArr && !get_member_count)) return context;
    context.params.update_member_count = true
    const countOneGroup = async (group:any) => {
        if (get_member_count || (!group.memberCount || (new Date().getTime() - new Date(group.memberCountAt).getTime() > 1000 * 60 * 60))) {
            const allMembers = await new CoreCall('grp-mbrs', context, {skipJoins: true}).find({
                query: {
                    $limit: 1,
                    group: group._id
                },
                admin_pass: true,
                skip_hooks: true
            })
                .catch(err => console.error(`Error updating member count for group ${context.result._id}: ${err.message}`))
            if (allMembers?.total) {
                return await new CoreCall('groups', context).patch(context.result._id as any, {
                    memberCount: allMembers.total,
                    memberCountAt: new Date()
                }, {
                    admin_pass: true,
                    skip_hooks: true  // This prevents the infinite loop
                })
                    .catch(err => {
                        console.error(`Error updating member count for group ${context.result._id}: ${err.message}`)
                        return group;
                    })
            }
        }
        return group
    }
    if(isArr) context.result = await Promise.all(context.result.map(a => countOneGroup(a)))
    else context.result = await countOneGroup(context.result)
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const groups = (app: Application) => {
    // Register our service on the Feathers application
    app.use(groupsPath,
        restMiddleware,
        new GroupsService(getOptions(app)), {
            // A list of all methods this service exposes externally
            methods: groupsMethods,
            // You can add additional custom events to be sent to clients here
            events: []
        })
    // Initialize hooks
    app.service(groupsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(groupsExternalResolver),
                schemaHooks.resolveResult(groupsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(groupsQueryValidator),
                schemaHooks.resolveQuery(groupsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                genKey,
                schemaHooks.validateData(groupsDataValidator),
                schemaHooks.resolveData(groupsDataResolver),
                handleOrgKeys

            ],
            patch: [
                memberUploads,
                schemaHooks.validateData(groupsPatchValidator),
                schemaHooks.resolveData(groupsPatchResolver),
                noOrgChange,
                genKey,
                handleOrgKeys
            ],
            remove: [handleOrgKeys]
        },
        after: {
            all: [joinOrg],
            get: [],
            create: [handleOrgKeys],
            patch: [
                handleOrgKeys,
                memberUploads,
                updateMemberCount
            ],
            remove: [removeMembers]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [groupsPath]: any
    }
}
