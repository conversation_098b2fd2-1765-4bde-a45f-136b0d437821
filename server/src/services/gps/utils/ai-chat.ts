
import {HookContext} from '../../../declarations.js';
import OpenAi from 'openai';
import {comparePlanTables} from '../../shops/chat/files.js';
import {CoreCall} from 'feathers-ucan';
import {pointToGeo} from '../../../utils/geo/index.js';
import {cmsMarketplaceSearch} from '../../marketplace/cms/index.js';

export const employer_plan_compare = async (context: HookContext) => {
    const {employer_plan} = context.params.runJoin || {};
    if (employer_plan) {
        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})

        context.params.query = employer_plan.query;
        const zip = context.params.query.place.zipcode
        const query:any = {
            $or: [{ geo: { $exists: false }}],
            type: 'mm',
            sim: true,
            public: true,
            $limit: 30,
            $sort: {fortyPremium: 1},
            fortyPremium: { $exists: true }
        }
        const drawers = await new CoreCall('junk-drawers', context).find({
            query: {
                $limit: 1,
                itemId: `zips|${zip.substring(0, 3)}`
            }
        })
            .catch(err => {
                console.log(`Error finding cost sim drawer: ${err.message}`)
                return {}
            })
        if (drawers.total) {
            const getObj = (z: any, tries = 0) => {
                const obj = drawers.data[0].data[z];
                if (obj && typeof obj === 'object') return obj;
                else if (tries < 50) return getObj(Number(z) + 1, tries + 1)
                else return {}
            }
            let obj = getObj(zip, 0);
            query.$or.push({['geo.geometry']: {$geoIntersects: {$geometry: pointToGeo(obj.lngLat)?.geometry}}})
        }
        const private_policies = await new CoreCall('coverages', context)._find({
            skip_hooks: true, admin_pass: true,
            query
        })

        const coverages:any[] = [];
        Object.keys(context.result.coverages).map(a => {
            coverages.push({_id: a, ...context.result.coverages[a]})
        })
        context.params.query = { ...context.params.query, $limit: 25 }
        const ctx = await cmsMarketplaceSearch(context)
        for (let i = 0; i < 25; i++) {
            if ((ctx.result.gold.data || [])[i]?.premium) coverages.push(ctx.result.gold.data[i])
            if ((ctx.result.silver.data || [])[i]?.premium) coverages.push(ctx.result.silver.data[i])
            if ((ctx.result.bronze.data || [])[i]?.premium) coverages.push(ctx.result.bronze.data[i])
        }

        for(const cov of private_policies.data){
            const premium = getCoverageRate({ coverage: cov, enrolled: context.params.query.household.people })
            coverages.push({...cov, premium})
        }

        const responseSchema = {
            type: 'object',
            patternProperties: {
                "^.*$": {
                    $comment: 'key is the employer plan id',
                    type: 'object',
                    patternProperties: {
                        "^.*$": {type: 'string', $comment: 'key is the similar individual market plan id',
                        }
                    }
                }
            }
        } as const

        const input = `
        ## Preface
        We need to identify the health plans from the individual market that are most similar to my employer plan options. All of the plans are provided in the plan details data, but the ids of the employer health plans are ${employer_plan.compare_ids.join(', ')}. Identify those plans by id in the below Plan Data section and compare the other plans against them. 
        
        ## Criteria
        1. Most important is carrier (Carrier Name - similar is ok such as "Blue Cross Blue Shield" being in both names).
        2. A close second is network structure (HMO, PPO, etc)
        3. A close third is the deductible amount.
        4. Some lesser considerations are: max out of pocket, coinsurance, and other benefit details.
        
        ## Response Structure
        Return the top 5 most similar plans (if there are 5 or more) for each of the employer plans, and a short description of why they are similar. Return the response as a JSON object. No commentary, just JSON. Make an object the employer plan id as the key and an object with the plan ids of the top similar plans as sub-keys and the short description as the value - matching this type {[employer_plan_id:string]: { [similar_plan_id]: string }}.
        
        ## Plan Data
        ${comparePlanTables([...coverages])}
        `
        const response = await openai.responses.create({
            model: 'gpt-4o',
            input
        })
            .catch(err => {
                console.error(`Error querying gps ai chat: ${err.message}`)
                throw new Error(`Error querying gps ai chat: ${err.message}`)
            })
        const output: any = response.output.filter(a => a.type === 'message');
        const content: any = output[0].content.filter(a => a.type === 'output_text')[0];

        const obj = JSON.parse(content.text.replace('```json', '').replace('```', '')?.trim().replace(/[']/g, '') || "{}")
        context.result = {...context.result, _fastjoin: {plan_compare: obj, coverages}};

    }
    return context;
}
