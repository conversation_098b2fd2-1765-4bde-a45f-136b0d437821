import { hooks as schemaHooks } from '@feathersjs/schema';
import {logChange} from '../../utils/change-log.js';
import {
    aiChatsQueryValidator,
    aiChatsResolver,
    aiChatsExternalResolver,
    aiChatsQueryResolver
} from '../../ai-chats.schema.js';

export const around = {
    resolveExternal: schemaHooks.resolveExternal(aiChatsExternalResolver),
    resolveResult: schemaHooks.resolveResult(aiChatsResolver)
};

export const before = {
    logChange: logChange(),
    validateQuery: schemaHooks.validateQuery(aiChatsQueryValidator),
    resolveQuery: schemaHooks.resolveQuery(aiChatsQueryResolver)
};

export const after = {};

export const error = {};
