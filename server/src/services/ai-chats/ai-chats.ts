// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    aiChatsDataValidator,
    aiChatsPatchValidator,
    aiChatsQueryValidator,
    aiChatsResolver,
    aiChatsExternalResolver,
    aiChatsDataResolver,
    aiChatsPatchResolver,
    aiChatsQueryResolver
} from './ai-chats.schema.js'

import type {Application} from '../../declarations.js'
import {AiChatsService, getOptions} from './ai-chats.class.js'
import {aiChatsPath, aiChatsMethods} from './ai-chats.shared.js'
import { HookContext } from '../../declarations.js';
import {allUcanAuth, anyAuth, CapabilityParts} from 'feathers-ucan';
import {logChange} from '../../../utils/change-log.js';

export * from './ai-chats.class.js'
export * from './ai-chats.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['accounts', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['accounts', '*']] as Array<CapabilityParts>;
    const ucanArgs:any = {
        get: anyAuth,
        create: anyAuth,
        find: anyAuth,
        patch: anyAuth,
        update: anyAuth,
        remove: anyAuth
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']]
    })(context) as any;
}

const setChatId = (context: HookContext) => {
    context.data.chatId = `${context.data.person}|${context.data.subject}|${context.data.chatName}`
    return context;
}



// A configure function that registers the service and its hooks via `app.configure`
export const aiChats = (app: Application) => {
    // Register our service on the Feathers application
    app.use(aiChatsPath, new AiChatsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: aiChatsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(aiChatsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(aiChatsExternalResolver),
                schemaHooks.resolveResult(aiChatsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(aiChatsQueryValidator),
                schemaHooks.resolveQuery(aiChatsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                setChatId,
                schemaHooks.validateData(aiChatsDataValidator),
                schemaHooks.resolveData(aiChatsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(aiChatsPatchValidator),
                schemaHooks.resolveData(aiChatsPatchResolver)
            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [aiChatsPath]: AiChatsService
    }
}
