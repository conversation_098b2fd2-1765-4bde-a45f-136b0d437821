// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    hostsDataValidator,
    hostsPatchValidator,
    hostsQueryValidator,
    hostsResolver,
    hostsExternalResolver,
    hostsDataResolver,
    hostsPatchResolver,
    hostsQueryResolver
} from './hosts.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {HostsService, getOptions} from './hosts.class.js'
import {hostsPath, hostsMethods} from './hosts.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {getJoin} from '../../../utils/fast-join.js';
import {logChange} from '../../../utils/change-log.js';
import {relate} from '../../../utils/relate/index.js';
import {scrub} from '../../../utils/sanitize/index.js';
import {scrubUploads} from '../../../utils/file-join.js';

export * from './hosts.class.js'
export * from './hosts.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['hosts', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['hosts', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: [...writer],
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    const cap_subjects:any = []
    if (!['get', 'find'].includes(context.method)) {
        let existing = {org: context.data.org};
        if (context.method !== 'create') {
            existing = await loadExists(context);
            context = setExists(context, existing);
        }
        const orgId = existing?.org || context.data.org
        const orgNamespace = `orgs:${orgId}`;
        cap_subjects.push(orgId)
        const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']]
        for (const w of orgWrite) {
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects,
        or: '*'
    })(context) as any;
}

const runJoins = async (context: HookContext) => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    const { host_org, w_support } = context.params.runJoin || {}
    if(host_org) {
        context = await  getJoin({ herePath: 'org', service: 'orgs'})(context);
    }
    if(w_support) {
        const herePath = w_support.planId ? (context.result.planSupport || {})[`${w_support.planId}`]?.team ? `plans.${w_support.planId}.team` : 'publicSupport' : 'publicSupport';
        context = await getJoin({
            herePath,
            service: 'teams',
            joinPath: 'team'
        })(context)
    }
    return context;
}

const relateOrg = async (context: HookContext) => {
    return relate('otm', { herePath: 'org', therePath: 'hostAccounts', thereService: 'orgs', paramsName: 'orgHostAccount'})(context);
}

const addDba = async (context: HookContext) => {
    if(!context.data.dba){
        const org = await new CoreCall('orgs', context, { skipJoins: true }).get(context.data.org);
        context.data.dba = org.name
    }
    return context;
}

const enableBids = async (context: HookContext) => {
    const { rfp } = (context.params.runJoin || {});
    if(rfp){
        await new CoreCall('plans', context).patch(rfp.planId, { $addToSet: { [`rfp.${rfp.role}.hosts`]: rfp.hostId }}, { admin_pass: true });
    }
    return context;
}

const imagePaths = ['avatar']
const uploadScrub = async (context: HookContext) => {
    const {host_videos} = context.params.runJoin || {};
    const paths: string[] = imagePaths;
    if (host_videos) {
        if (host_videos === '*') paths.push('videos.*')
        else {
            for (let i = 0; i < host_videos.length; i++) {
                paths.push(`videos.${host_videos[i]}`);
            }
        }
    }
    return await scrubUploads({paths})(context)
}

const addSettings = (context: HookContext) => {
    context.data.shopStatuses = {
        'unassigned': { label: 'Unassigned', color: 'ir-yellow' },
        'assigned': { label: 'Assigned', color: 'ir-light-blue' },
        'urgent': { label: 'Urgent', color: 'ir-orange'},
        'complete': { label: 'Complete', color: 'ir-green' },
        'lost': { label: 'Lost', color: 'black' }
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const hosts = (app: Application) => {
    // Register our service on the Feathers application
    app.use(hostsPath, new HostsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: hostsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(hostsPath).hooks({
        around: {
            all: [schemaHooks.resolveExternal(hostsExternalResolver), schemaHooks.resolveResult(hostsResolver)]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(hostsQueryValidator),
                schemaHooks.resolveQuery(hostsQueryResolver),
                scrubUploads({paths: imagePaths}),

            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(hostsDataValidator),
                schemaHooks.resolveData(hostsDataResolver),
                scrub(['description']),
                addDba,
                addSettings,
                relateOrg,
            ],
            patch: [
                schemaHooks.validateData(hostsPatchValidator),
                schemaHooks.resolveData(hostsPatchResolver),
                scrub(['description']),
                relateOrg,
                enableBids
            ],
            remove: [relateOrg]
        },
        after: {
            all: [
                scrubUploads({paths: imagePaths}),
                runJoins
            ],
            create: [relateOrg],
            patch: [relateOrg],
            remove: [relateOrg]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [hostsPath]: HostsService
    }
}
