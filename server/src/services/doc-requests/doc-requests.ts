// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    docRequestsDataValidator,
    docRequestsPatchValidator,
    docRequestsQueryValidator,
    docRequestsResolver,
    docRequestsExternalResolver,
    docRequestsDataResolver,
    docRequestsPatchResolver,
    docRequestsQueryResolver
} from './doc-requests.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {DocRequestsService, getOptions} from './doc-requests.class.js'
import {docRequestsPath, docRequestsMethods} from './doc-requests.shared.js'

export * from './doc-requests.class.js'
export * from './doc-requests.schema.js'
import {logChange} from '../../../utils/change-log.js';
import {allUcanAuth, CapabilityParts, loadExists, noThrow, setExists} from 'feathers-ucan';

import {runSim} from './utils/claim-sim.js';
import {smbPtc} from './utils/smb-ptc.js';

const authenticate = async (context: HookContext) => {
    const writer = [['doc-requests', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['doc-requests', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: noThrow,
        patch: noThrow,
        update: [...writer],
        remove: [...deleter]
    };

    if (['patch', 'remove'].includes(context.method)) {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        if (!ex.updatedBy?.login) {
            ucanArgs.patch = noThrow as any;
            ucanArgs.remove = noThrow as any;
        }
    }

    return await allUcanAuth<HookContext>(ucanArgs, {
        creatorPass: ['*'],
        loginPass: [[['updatedBy.login'], ['patch']]],
        adminPass: ['patch', 'create', 'remove'],
        or: '*'
    })(context) as any;
}

import multer from 'multer';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    req.feathers.file = req.file;
    req.feathers.runJoin = req.query.runJoin
    req.feathers.core = req.query.core
    return next();
}

import { uploadEmployees } from './utils/upload-employees.js';

// A configure function that registers the service and its hooks via `app.configure`
export const docRequests = (app: Application) => {
    // Register our service on the Feathers application
    app.use(docRequestsPath,
        restMiddleware,
        new DocRequestsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: docRequestsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(docRequestsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(docRequestsExternalResolver),
                schemaHooks.resolveResult(docRequestsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(docRequestsQueryValidator), schemaHooks.resolveQuery(docRequestsQueryResolver)],
            find: [],
            get: [runSim],
            create: [
                schemaHooks.validateData(docRequestsDataValidator),
                schemaHooks.resolveData(docRequestsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(docRequestsPatchValidator),
                schemaHooks.resolveData(docRequestsPatchResolver),
                uploadEmployees
            ],
            remove: []
        },
        after: {
            all: [],
            get: [smbPtc()],
            patch: [smbPtc(), uploadEmployees]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [docRequestsPath]: any
    }
}
