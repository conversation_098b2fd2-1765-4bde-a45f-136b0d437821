import {HookContext} from '../../../../declarations.js';
import {<PERSON><PERSON>} from "@moovio/sdk";
import {CoreCall} from 'feathers-ucan';
import {TransferCreate, TransferOptionsCriteria} from '@moovio/node/lib/types/transfers.js';
import axios from 'axios';
import {AnyObj} from '../../utils/types.js';


const getUuid = () => {
    const rand = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, c => {
        const r = Math.random() * 16 | 0;
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });

    const timestamp = String(new Date().getTime());
    const combined = rand.slice(0, rand.length - timestamp.length) + timestamp;

    const formatted = [8, 12, 16, 20].reduce((str, idx, i) =>
        str.slice(0, idx + i) + '-' + str.slice(idx + i), combined);

    return formatted.slice(0, 10) + '4' + formatted.slice(11, 15) + '4' + formatted.slice(16);
};


const getMoov = () => {
    return (context: HookContext) => {
        const mv = context.app.get('banking').moov;
        const suffix = context.app.get('env') === 'dev' ? '_dev' : ''
        const security: any = {
            username: mv[`public_key${suffix}`],
            password: mv[`secret_key${suffix}`],
        };
        let moov;
        try {
            moov = new Moov({security})
        } catch (err: any) {
            console.log('Error creating moov instance', err)
            throw new Error(`Error creating moov instance: ${err.message}`)
        }
        return moov;
    }
}

const getMoovApi = (headers?: any) => {
    return (context: HookContext) => {
        const mv = context.app.get('banking').moov;
        const suffix = context.app.get('env') === 'dev' ? '_dev' : ''

        return axios.create({
            baseURL: 'https://api.moov.io',
            headers: {
                'Authorization': `Basic ${btoa(`${mv[`public_key${suffix}`]}:${mv[`secret_key${suffix}`]}`)}`,
                'Content-Type': 'application/json',
                ...headers
            }
        })
    }
}

export const get_moov_id = () => {
    return async (context: HookContext) => {
        const mv = context.app.get('banking').moov;
        context.result = {_id: mv.account_id, accountID: mv.account_id};
        return context;
    }
}

export const get_mcc = () => {
    return async (context: HookContext) => {
        const moov = getMoovApi()(context);
        const res = await moov.get('/industries')
        context.result = {_id: getUuid(), industries: res.data.industries}
        return context;
    }

}

export const bankLookup = async (context: HookContext): Promise<HookContext> => {
    const moov = getMoov()(context);
    const {routingNumber} = context.params.query;
    const inst = await moov.institutions.search({routingNumber}, 'ach')
        .catch(err => {
            console.error(err);
            return {achParticipants: []}
        });
    const data = inst.result.achParticipants || [];
    context.result = {total: data.length, data}
    return context;
}

const accountPayload = {
    accountType: "business",
    profile: {
        business: {
            legalBusinessName: "Whole Body Fitness LLC",
            businessType: "llc",
            website: "wbfllc.com"
        }
    },
    foreignId: "your-correlation-id"
};


// =======================================================================
// Adapted from Stripe's index.ts file
// =======================================================================


// -----------------------------------------------------------------------
// Accounts
// -----------------------------------------------------------------------

export const get_tos_token = () => {
    return async (context: HookContext) => {
        // const moov = getMoov()(context);
        const moov = getMoovApi()(context);
        // const res = await moov.accounts.getTermsOfServiceToken({
        //     origin: context.params.headers.origin
        // })
        const res = await moov.get('/tos-token', {
            headers: {
                origin: context.params.headers.origin,
                referer: context.params.headers.referer || context.params.headers.origin
            }
        })
            .catch(err => {
                console.log(`Error getting tos token: ${err.message}`)
            })
        context.result = {_id: getUuid(), ...res?.data}
        return context;
    }
}
type TokenOptions = {
    refreshToken?: string,
    scopes?: string,
    grantType?: string
}
export const get_drop_token = (tokenOptions?: TokenOptions) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        try {
            const mv = context.app.get('banking').moov;

            const res = await moov.authentication.createAccessToken({
                grantType: 'client_credentials',
                clientId: mv.public_key,
                clientSecret: mv.secret_key,
                scope: '/accounts.write',
                ...tokenOptions
            })
            context.result = {_id: res.result.accessToken, token: res.result.accessToken, facilitatorID: mv.account_id, ...res.result};
        } catch (err: any) {
            console.log(`Error generating moov drop account token: ${err.message}`)
        }
        return context;
    }
}

export const get_facilitator_account_id = () => {
    return (context: HookContext) => {
        const mv = context.app.get('banking').moov;
        context.result = {_id: mv.account_id};
        return context;
    }
}


/** accountID is the treasuryId - moov account id */
// @Tyler: TODO - caps argument should be removed or normalized to match Moov Capability schema
// Moov ENUMS for capabilities: [ CAPABILITIES.TRANSFERS, CAPABILITIES.SEND_FUNDS, CAPABILITIES.COLLECT_FUNDS, CAPABILITIES.WALLET, '1099', 'card-issuing' ]
//   https://docs.moov.io/api/moov-accounts/capabilities/list/
//   https://docs.moov.io/node/capabilities/#capabilities
export const add_capabilities = (accountID: string, caps?: any) => {
    return async (context: HookContext) => {

        const moov = getMoov()(context)
        const capabilities = caps || [
            // CAPABILITIES.TRANSFERS,
            // CAPABILITIES.SEND_FUNDS,
            // CAPABILITIES.COLLECT_FUNDS,
            // CAPABILITIES.WALLET,
            // '1099',
            'transfers',
            'send-funds',
            // 'collect-funds',
            'wallet',
            'card-issuing'
        ]

        const capReq = await moov.capabilities.request({accountID, addCapabilities: {capabilities}})
            .catch(err => {
                console.error(`Error requesting moov capabilities (axios) for account id ${accountID}: ${err.message}`)
                return {data: context.result}
            })
        context.result = capReq.result;
        return context;
    }
}

export const get_capability = (capabilityID:string) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const cap = await moov.capabilities.get({accountID: context.id as string, capabilityID })
            .catch(err => {
                console.error(`Error getting moov capability ${capabilityID} for account id ${context.id}: ${err.message}`)
                throw new Error(`Error getting moov capability ${capabilityID} for account id ${context.id}: ${err.message}`);
            })
        context.result = {_id: getUuid(), ...cap.result}
        return context;
    }
}

/** Creates a Moov account for a BUSINESS (org) (not for an INDIVIDUAL) */
/** TODO: TOS acceptance: see https://docs.moov.io/guides/accounts/requirements/platform-agreement/ */
export const accounts_create = (orgId: any, data?: any) => {
    return async (context: HookContext) => {

        const org = await new CoreCall('orgs', context).get(orgId);
        const moov = getMoov()(context);

        // const moov = getMoov()(context)
        const company = orgToCompany(org);

        const accounts = await moov.accounts.list({foreignID: String(org._id)})
            .catch(err => console.log(`Error searching accounts before moov account create: ${err.message}`));
        let account: any
        if (accounts?.result?.length) account = accounts.result[0];
        else {
            const {termsOfService, ...rest} = data;
            const created = await moov.accounts.create({
                accountType: 'business',
                profile: {
                    business: company
                    // @Tyler: representatives = array<Representative (Moov)>

                },
                // metadata: {},
                // termsOfService: null, // @Tyler: Need to get TOS acceptance token from Moov.js (client-side)
                foreignID: String(orgId),
                ...rest
                // customerSupport: null,
                // settings: null // OR { cardPayment: { statementDescriptor: 'Description to display on credit card transactions' } }
            })
                .catch(err => {

                    console.log(err);
                    return {result: account}
                })
            account = created.result;
        }
        context.result = account;
        await new CoreCall('orgs', context, {skipJoins: true}).patch(org._id, {$set: {'treasury.id': account.accountID}})
        // context.data.$set = { ...context.data.$set, ['stripe.accounts.account.id']: account.id }
        return context;
    }
}


export const disable_capability = (accountID: string, cap: any) => {
    return async (context: HookContext) => {

        const moov = getMoov()(context)

        const acct = await moov.capabilities.disable({accountID, capabilityID: cap})
            .catch(err => {
                console.error(`Error disabling treasury capabilities for account id ${accountID}: ${err.message}`)
                return context.result
            })
        context.result = acct.result;
        return context;
    }
}


// @Jordan: TODO
//this is just a consolidation of several of the above steps
export const account_setup = (org: any, {capabilities, tos}) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context)
        context = await accounts_create(org, {skip_hooks: true, admin_pass: true})(context);
        context = await add_capabilities(context.result.accountID, capabilities)(context);
        const updated = await moov.accounts.update({
            accountID: context.result.accountID,
            patchAccount: {termsOfService: tos}
        })

        context.result = {account: updated.result}
        return context;
    }
}


/** Get Moov Account (Treasury acct) details */
export const get_account = (id: string) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const acct = await moov.accounts.get({
            accountID: id || context.id as string
        });
        context.result = acct.result;
        return context;
    }
}


// @Tyler: ensure params schema matches Moov "Account" schema
//   see: https://docs.moov.io/node/accounts/#account
export const account_update = (params: any) => {
    return async (context: HookContext) => {
        // const moov = getMoovApi()(context);
        const moov = getMoov()(context);
        const acct = await moov.accounts.update({accountID: context.id, patchAccount: params})

        // const acct = await moov.patch(`/accounts/${context.id}`, params)
            .catch(err => {
                console.error(`Error updating moov account: ${err.message}`)
                throw new Error(`Error updating moov account: ${err.message}`);
            })
        context.result = acct.data.result;
        // context.result = acct.data;
        return context;
    }
}

export const add_files = () => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const res = await moov.files.upload({
            accountID: context.id,
            fileUploadRequestMultiPart: {
                file: context.params.file,
                filePurpose: context.params.banking.filePurpose
            }
        })
        context.result = res.result;
        return context;
    }
}

export const list_files = () => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const files = await moov.files.list({accountID: context.id})
        context.result = files.result;
        return context;
    }
}



export const account_get = (id: string) => {
    return async (context: any) => {
        const moov = getMoov()(context);
        const acct = await moov.accounts.get({
            accountID: id || context.id
        });
        context.result = acct.result;
        return context;
    }
}

export const get_wallet = (accountID: string, walletID?: string) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        let wallet;
        if (walletID) {
            const got = await moov.wallets.get({accountID, walletID})
                .catch(err => {
                    throw new Error(`Error getting wallet: ${err.message}`)
                });
            wallet = got.result;
        } else {
            const list = (await moov.wallets.list({accountID})
                .catch(err => {
                    throw new Error(`Error listing wallet: ${err.message}`)
                }))
            wallet = list.result[0]
        }

        context.result = wallet;
        return context;
    }
}

export const get_wallet_transactions = (accountID: string, walletID: string, query?: AnyObj) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        for(const k in (query || {}) as object){
            if(['createdStartDateTime', 'createdEndDateTime'].includes(k)) (query as any)[k] = new Date((query as any)[k])
        }
        const transactions = await moov.walletTransactions.list({accountID, walletID, ...query})
        context.result = {_id: accountID, data: transactions.result}
        return context;
    }
}
export const get_wallet_transaction = (accountID: string, walletID: string, transactionID: string) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const t = await moov.walletTransactions.get({accountID, walletID, transactionID})
        context.result = t.result
        return context;
    }
}


// -----------------------------------------------------------------------
// Representatives
// -----------------------------------------------------------------------

// @Tyler: I'm assuming that Moov's "representative" ≈ Stripe's "person"
// @Tyler: fn name changed: list_persons -> list_representatives
export const list_representatives = (accountID: string, opts?: any) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const data = await moov.representatives.list({accountID});
        context.result = {_id: getUuid(), data: data.result, total: data.result.length};
        // console.log('after list reps', context);
        return context;
    }
}


// @Tyler: fn name changed: get_person -> get_representative
export const get_representative = (accountID: string, representativeID: string) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const rep = await moov.representatives.get({accountID, representativeID});
        context.result = {_id: representativeID, ...rep.result || {}};
        return context;

    }
}


// @Tyler: fn name changed: create_person -> create_representative
// @Tyler: Review together -- this doesn't match well.  I think pplId doesn't work - Moov creates an ID(?)
export const create_representative = (accountID: string, pplId: string, person: any) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context)
        const rep = await moov.representatives.create({
            accountID,
            createRepresentative: {
                responsibilities: {
                    isController: false,
                    isOwner: false,
                    // ownershipPercentage: 0, -- required if isOwner == true
                    jobTitle: '' // <= 64 chars
                },
                ...person,
            }
        });
        context.result = {_id: rep?.representativeID, ...rep?.result}
        await new CoreCall('ppls', context, {skipJoins: true}).patch(pplId, {
            $set: {
                [`moovAccounts.${context.result.account}`]: {
                    id: context.result.accountID,
                    isController: person.responsibilities?.isController
                }
            }
        }, {admin_pass: true});
        return context;
    }
}


// @Tyler: fn name changed: update_person -> update_representative
// @Tyler: changes to person's role (responsibilities) not currently captured
// @Tyler: this isn't a true patch operation -- it will send ALL non-null fields
export const update_representative = (accountID: string, representativeID: string, updateRepresentative: any) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context)
        const rep = await moov.representatives.update({accountID, representativeID, updateRepresentative});
        context.result = {_id: representativeID, ...rep.result}
        return context;
    }
}


// @Tyler: fn name changed: delete_person -> delete_representative
export const delete_representative = (accountID: string, representativeID: string) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const rep: any = await moov.representatives.delete({accountID, representativeID});
        await new CoreCall('ppls', context)._patch(null, {$unset: {[`moovAccounts.${accountID}`]: ''}}, {
            skip_hooks: true,
            admin_pass: true,
            query: {[`moovAccounts.${accountID}.id`]: accountID}
        });
        context.result = {_id: representativeID, ...rep.result}

        return context
    }
}


// -----------------------------------------------------------------------
// Bank Accounts
// -----------------------------------------------------------------------

export const get_external_account = (bankAccountID: string) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const acct = await moov.bankAccounts.get({accountID: context.id as string, bankAccountID});
        context.result = {_id: bankAccountID, ...acct.result};
        return context;
    }
}


// export const update_external_account = (accountID: string, bankAccountID: string, params: any) => {
//     return async (context: HookContext) => {
//         const moov = getMoov()(context);
//         context.result = await moov.bankAccounts.update(accountID, bankAccountID, params)
//         return context;
//     }
// }


type CreateBankAccountOptions = {
    ccBankAccountId: string,
    plaidToken?: string,
    mxAuthCode?: string
}

export const complete_verify = (code, accountID: string, bankAccountID: string, ccBankAccountID: string) => {
    return async (context: HookContext) => {
        const moov = getMoovApi()(context);
        await new CoreCall('bank-accounts', context).get(ccBankAccountID)
            .catch(err => {
                throw new Error(`Include bank account number to link verify result to - provided ${ccBankAccountID}: ${err.message}`)
            })
        const res = await moov.put(`/accounts/${accountID}/bank-accounts/${bankAccountID}/verify`, {code})
            .catch(err => {
                console.log(`Error verifying account: ${err.message}`)
                throw new Error(err.message)
            })

        let acctId = ccBankAccountID
        if (!acctId) {
            const ba = await new CoreCall('bank-accounts', context).find({
                query: {
                    $limit: 1,
                    moov_link_id: bankAccountID
                }
            })
                .catch(err => console.log(`Error getting bank account to patch moov verify status for account ${accountID} - bankaccountID ${bankAccountID}: ${err.message}`))
            if (ba.total) acctId = ba.data[0]._id
        }
        const $set: any = {['verification.verified']: res.data.status === 'successful'}
        for (const k in res.data || {}) {
            $set[`verification.${k}`] = res.data[k]
        }
        await new CoreCall('bank-accounts', context).patch(acctId, {$set})

        return res.data;
    }
}
export const check_verify = (accountID: string, bankAccountID: string, ccBankAccountID?: string) => {
    return async (context: HookContext) => {
        const moov = getMoovApi()(context);

        const res = await moov.get(`/accounts/${accountID}/bank-accounts/${bankAccountID}/verify`)

        let acctId = ccBankAccountID
        if (acctId) {
            const $set: any = {}
            for (const k in res.data || {}) {
                $set[`verification.${k}`] = res.data[k]
            }
            await new CoreCall('bank-accounts', context).patch(acctId, {$set})
        }
        return res.data;
    }
}

/** First choice for verification - instant verify */
export const init_verify = (accountID: string, bankAccountID: string, ccBankAccountID?: string) => {
    return async (context: HookContext) => {
        const moov = getMoovApi({'X-Wait-For': 'rail-response'})(context);
        const res = await moov.post(`/accounts/${accountID}/bank-accounts/${bankAccountID}/verify`)
            .catch(err => {
                console.log(`Verify not initiated for bank account: ${bankAccountID} | ${err.message}`)
                return {data: {}}
            })
        let acctId = ccBankAccountID
        if (acctId) {
            const $set: any = {}
            for (const k in res.data || {}) {
                $set[`verification.${k}`] = res.data[k]
            }
            await new CoreCall('bank-accounts', context).patch(acctId, {$set})
        }
        context.result = {_id: getUuid(), ...res.data}
        return context;
    }
}

/**Fallback verification chioce - micro deposits */
export const init_micro = (accountID: string, bankAccountID: string, ccBankAccountID?: string) => {
    return async (context: HookContext) => {
        const moov = getMoovApi({'X-Wait-For': 'rail-response'})(context);
        const res = await moov.post(`/accounts/${accountID}/bank-accounts/${bankAccountID}/micro-deposits`)
            .catch(err => {
                console.log(`Micro deposits not intiated for bank account: ${bankAccountID} | ${err.message}`)
                return {data: {}}
            })
        let acctId = ccBankAccountID
        if (acctId) {
            const map = {'204': 'sent-credit',}
            await new CoreCall('bank-accounts', context).patch(acctId, {
                $set: {
                    [`verification.verificationMethod`]: 'micro',
                    ['verification.status']: map[res['status']] || 'failed'
                }
            })
        }
        context.result = {_id: getUuid(), ...res.data}
        return context;
    }
}

export const complete_micro = (amounts: [number, number], accountID: string, bankAccountID: string, ccBankAccountID: string) => {
    return async (context: HookContext) => {
        const moov = getMoovApi({'X-Wait-For': 'rail-response'})(context);
        await new CoreCall('bank-accounts', context).get(ccBankAccountID)
            .catch(err => {
                throw new Error(`Include bank account number to link micro deposit result to - provided ${ccBankAccountID}: ${err.message}`)
            })
        const res: any = await moov.put(`/accounts/${accountID}/bank-accounts/${bankAccountID}/micro-deposits`, {amounts})
            .catch(err => {
                console.log(`Micro deposits not confirmed for bank account: ${bankAccountID} | ${err.message}`)
            })
        await new CoreCall('bank-accounts', context).patch(ccBankAccountID, {
            $set: {
                [`verification.verificationMethod`]: 'micro',
                ['verification.status']: res['data'].status || 'failed',
                ['verification.verified']: res['data'].status === 'verified'
            }
        })
        context.result = {_id: getUuid(), ...res.data}
        return context;
    }
}


export const create_external_account = (bankAccount: any, options: CreateBankAccountOptions) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context)

        const accts = await moov.bankAccounts.list({accountID: context.id as string});

        let bankAcct;

        for (const acct of accts.result) {
            if (acct.routingNumber === bankAccount.routingNumber && acct.lastFourAccountNumber === bankAccount.accountNumber.slice(-4)) {
                bankAcct = acct
                break;
            }
        }

        if (!bankAcct) {
            const acct = await moov.bankAccounts.link({accountID: context.id as string, linkBankAccount: {account: bankAccount}})
                .catch(err => {
                    console.log(`Error linking bank account: ${err.message}`)
                    throw new Error(`Error linking bank account: ${err.message}`);
                })
            bankAcct = acct.result;
        }

        const micro: any = await init_verify(context.id as string, bankAcct.bankAccountID)(context)
            .catch(err => {
                console.log(`Could not init micro deposits on link account: ${err.message}`)
                return;
            })
        if (Object.keys(micro?.result || {}).length) context.result.verification = micro.result;

        context.result = {_id: bankAcct?.bankAccountID, ...bankAcct};
        return context;
    }
}


// @Tyler: fn name changed: delete_external_account -> disable_external_account
export const delete_external_account = (accountID: string, bankAccountID: string) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const acct: any = await moov.bankAccounts.disable({accountID, bankAccountID})
        context.result = {_id: bankAccountID, ...acct.result}
        return context;
    }
}


// -----------------------------------------------------------------------
// Moov Wallets
// -----------------------------------------------------------------------

// @Tyler: Moov creates the account's wallet automatically when the "wallet" capability is enabled for the account
//  see: https://docs.moov.io/guides/sources/wallets/
// export const create_wallet = (params: any) => {
//     return async (context: HookContext) => {
//         const moov = getMoov()(context, {stripeAccount: context.id as string})
//         context.result = await moov.
//         context.result = await moov.treasury.financialAccounts.create({supported_currencies: ['usd'], ...params});
//         return context;
//     }
// }


// export const update_financial_account = (faId: string, params: any) => {
//     return async (context: HookContext) => {
//         const moov = getMoov()(context, {stripeAccount: context.id as string});
//         context.result = await moov.treasury.financialAccounts.update(faId, params);
//         return context;
//     }
// }


// @Tyler: fn name changed: get_multiple_fas -> list_wallets
export const list_wallets = (accountID: string) => {
    return async (context: HookContext) => {
        context.result = {data: []};
        const moov = getMoov()(context)
        const targetAccountID = accountID || context.id as string;

        console.log(`Attempting to list wallets for account: ${targetAccountID}`);

        // First, let's check if the account exists and has wallet capability
        try {
            const account = await moov.accounts.get({accountID: targetAccountID});
            console.log(`Account found: ${account.result?.accountID}`);
            console.log(`Account capabilities:`, account.result?.capabilities?.map(c => c.capability) || []);

            const hasWalletCap = account.result?.capabilities?.some(c => c.capability === 'wallet');
            if (!hasWalletCap) {
                console.error(`Account ${targetAccountID} does not have wallet capability enabled`);
                throw new Error(`Account does not have wallet capability enabled`);
            }
        } catch (accountErr) {
            console.error(`Error checking account ${targetAccountID}:`, accountErr);
            throw accountErr;
        }

        const wallets = await moov.wallets.list({accountID: targetAccountID})
            .catch(err => {
                console.error(`Issue listing Moov Wallet(s) for account ${targetAccountID}:`);
                console.error(`Error message: ${err.message}`);
                console.error(`Error status: ${err.status || 'unknown'}`);
                console.error(`Error response: ${JSON.stringify(err.response?.data || err.response || 'no response data')}`);
                console.error(`Full error:`, err);
                return undefined
            });
        context.result = {_id: getUuid(), data: wallets?.result || []}
        return context;
    }
}

// -----------------------------------------------------------------------
// Payment Methods
// -----------------------------------------------------------------------

type ListPmsQuery = {
    sourceID?: string,
    paymentMethodType?: string
}
export const list_pms = (accountID: string, query?: ListPmsQuery) => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const list = await moov.paymentMethods.list({accountID, ...query})
        context.result = {_id: getUuid(), data: list.result || []}
        return context;
    }
}

export const payment_methods = () => {
    return async (context: HookContext) => {
        const moov = getMoov()(context);
        const list = await moov.paymentMethods.list({accountID: context.id as string})
        context.result = {_id: getUuid(), data: list.result || []}
        return context;
    }
}


// -----------------------------------------------------------------------
// Transactions
// -----------------------------------------------------------------------


export const transfer_options = (transfer: TransferOptionsCriteria) => {
    return async (context: HookContext) => {
        // const moov = getMoov()(context);
        const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)

            const opts = await moov.post(`/accounts/${context.id}/transfer-options`, transfer)
        // const opts = await moov.transfers.generateOptions({
        //     accountID: context.id as string,
        //     createTransferOptions: transfer
        // })
            .catch(err => {
                console.error(`Error getting transfer options: ${err.message}`)
                throw new Error(`Error getting transfer options: ${err.message}`);
            })
        context.result = {_id: getUuid(), ...opts.data.result}
        return context
    }
}
export const create_transfer = (transfer: TransferCreate) => {
    return async (context: HookContext) => {

        // Issue API request
        // const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)
        const moov = getMoov()(context);
        // const res = await moov.post(`/accounts/${context.id}/transfers`, transfer)
        const res = await moov.transfers.create({accountID: context.id as string, xIdempotencyKey: getUuid(), xWaitFor: 'rail-response', createTransfer: transfer, })
            .catch((err: any) => {
                throw new Error(`Error creating outbound payment: ${err.message}`);
            });
        // context.result = await moov.transfers.create(transfer, getUuid())
        context.result = {_id: res.data.transferID || getUuid(), ...res.data};
        return context;
    }
}

// -----------------------------------------------------------------------
// CARD ISSUING
// -----------------------------------------------------------------------

type IssueCard = {
    fundingWalletID: string,
    authorizedUser: {
        firstName: string;
        lastName: string;
        birthDate: {
            day: number;
            month: number;
            year: number;
        };
    };
    controls: {
        singleUse: boolean;
        velocityLimits: {
            amount: number;
            interval: 'per-transaction';
        }[];
    };
    expiration?: {
        month: number;
        year: number;
    };
    memo?: string;
};
export const issue_card = (data: IssueCard) => {
    return async (context: HookContext) => {
        // const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)
        // context.result = await moov.post(`/issuing/${context.id}/issued-cards`, {
        //     formFactor: 'virtual',
        //     ...data
        // })
        const moov = getMoovApi()(context)
        const card = await moov.post(`/accounts/${context.id}/issued-cards`, {
            formFactor: 'virtual',
            ...data
        })
            .catch(err => {
                console.error(`Error creating spend card: ${err.message}`)
                throw new Error(`Error creating spend card: ${err.message}`);
            })
        context.result = card.data;
        return context;
    }
}

type UpdateCard = {
    authorizedUser: {
        firstName: string;
        lastName: string;
        birthDate: {
            day: number;
            month: number;
            year: number;
        };
    };
    state: 'active' | 'inactive' | 'closed' | 'pending-verification';
    memo: string;
};
export const update_issued_card = (card_id: string, data: UpdateCard) => {
    return async (context: HookContext) => {
        const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)
        const patched = await moov.patch(`/issuing/${context.id}/issued-cards/${card_id}`, {
            ...data
        })
            // const moov = getMoov()(context)
            // context.result = await moov.cardIssuing.update({
            //     accountID: context.id,
            //     issueCardID: card_id,
            //     ...data
            // })
            .catch(err => {
                console.error(`Error updating spend card: ${err.message}`)
                throw new Error(`Error updating spend card: ${err.message}`);
            })
        context.result = patched.data;
        return context;
    }
}

export const get_spend_card = (card_id: string) => {
    return async (context: HookContext) => {
        const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)
        const card = await moov.get(`/issuing/${context.id}/issued-cards/${card_id}`)
            // const moov = getMoov()(context)
            // context.result = await moov.cardIssuing.get({
            //     accountID: context.id,
            //     issueCardID: card_id
            // })
            .catch(err => {
                console.error(`Error getting spend card: ${err.message}`)
                throw new Error(`Error getting spend card: ${err.message}`);
            })
        context.result = card.data;
        return context;
    }
}

type ListCardsQuery = {
    skip?: number,
    count?: number,
    state?: 'active' | 'pending-verification' | 'inactive' | 'closed',
}
export const list_spend_cards = (query: ListCardsQuery) => {
    return async (context: HookContext) => {
        const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)
        const list = await moov.get(`/issuing/${context.id}/issued-cards`)
            // const moov = getMoov()(context)
            // const data = await moov.cardIssuing.list({
            //     accountID: context.id,
            //     ...query
            // })
            .catch(err => {
                console.error(`Error listing spend cards: ${err.message}`)
                throw new Error(`Error listing spend cards: ${err.message}`);
            })
        context.result = {_id: getUuid(), data: list.data, has_more: list.data.length >= (query.count || 200)};
        return context;
    }
}

type ListCardQuery = {
    skip: number,
    count: number,
    issuedCardID: string,
    startDateTime: string,
    endDateTime: string
}
export const list_spend_card_transactions = (query: ListCardQuery) => {
    return async (context: HookContext) => {
        const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)
        const list = await moov.get(`/issuing/${context.id}/card-transactions`, {
            params: query
        })
            // const moov = getMoov()(context)
            // const data = await moov.issuingTransactions.list({
            //     accountID: context.id,
            //     ...query
            // })
            .catch(err => {
                console.error(`Error listing spend transactions: ${err.message}`)
                throw new Error(`Error listing spend transactions: ${err.message}`);
            })
        context.result = {_id: getUuid(), data: list.data, has_more: list.data.length >= (query.count || 200)};
        return context;
    }
}

export const get_spend_transaction = (transaction_id: string) => {
    return async (context: HookContext) => {
        // const moov = getMoov()(context)
        const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)
        const transaction = await moov.get(`/issuing/${context.id}/card-transactions/${transaction_id}`)
            .catch(err => {
                console.error(`Error getting spend transaction: ${err.message}`)
                throw new Error(`Error getting spend transaction: ${err.message}`);
            })
            // context.result = await moov.issuingTransations.get({
            //     accountID: context.id,
            //     cardTransactionID: transaction_id
            // })
            .catch(err => {
                console.error(`Error getting spend transaction: ${err.message}`)
                throw new Error(`Error getting spend transaction: ${err.message}`);
            })
        context.result = transaction.data;
        return context;
    }
}

type Status = 'pending' | 'declined' | 'canceled' | 'cleared' | 'expired'
export const list_spend_card_auths = (query: ListCardQuery & { statuses: Array<Status> }) => {
    return async (context: HookContext) => {
        // const moov = getMoov()(context)
        const moov = getMoovApi({'X-Idempotency-Key': getUuid(), 'X-Wait-For': 'rail-response'})(context)
        const list = await moov.get(`/issuing/${context.id}/card-authorizations`, {
            params: query
        })
            // const data = await moov.issuingTransactions.listAuthorizations({
            //     accountID: context.id,
            //     ...query
            // })
            .catch(err => {
                console.error(`Error listing spend transactions: ${err.message}`)
                throw new Error(`Error listing spend transactions: ${err.message}`);
            })

        return {data: list.data, has_more: list.data.length >= (query.count || 200), _id: getUuid()};
    }
}

export const get_onboarding_link = (accountID: string) => {
    return async (context: HookContext) => {
        const moov = getMoovApi()(context);
        const res = await moov.get(`/accounts/${accountID}/onboarding-invites`)
            .catch(err => {
                console.error(`Error getting onboarding link: ${err.message}`)
                throw new Error(`Error getting onboarding link: ${err.message}`);
            })
        context.result = {_id: getUuid(), ...res.data}
        return context;
    }
}


