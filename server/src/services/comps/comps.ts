// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  compsDataValidator,
  compsPatchValidator,
  compsQueryValidator,
  compsResolver,
  compsExternalResolver,
  compsDataResolver,
  compsPatchResolver,
  compsQueryResolver
} from './comps.schema.js'

import { Application, HookContext } from '../../declarations.js'
import { CompsService, getOptions } from './comps.class.js'
import { compsPath, compsMethods } from './comps.shared.js'
import {logChange} from '../../../utils/change-log.js';
import {scrub} from '../../../utils/sanitize/index.js';
import {
  allUcanAuth,
  anyAuth,
  CapabilityParts,
  CoreCall,
  loadExists,
  noThrowAuth,
  setExists
} from 'feathers-ucan'

export * from './comps.class.js'
export * from './comps.schema.js'

const authenticate = async (context: HookContext) => {
  const creator = [['comps', 'WRITE']] as Array<CapabilityParts>
  const deleter = [['comps', '*']] as Array<CapabilityParts>
  const ucanArgs = {
    create: anyAuth,
    patch: [...creator],
    update: [...creator],
    remove: [...deleter]
  }
  const cap_subjects: any = []
  if (['patch', 'update', 'remove'].includes(context.method)) {
    const existing = await loadExists(context)
    context = setExists(context, existing)
    const orgId = existing.org
    if (orgId) {
      cap_subjects.push(orgId)
      const orgWrite: CapabilityParts = [`orgs:${orgId}`, 'WRITE']
      ucanArgs.patch.unshift(orgWrite)
      ucanArgs.update.unshift(orgWrite)
      ucanArgs.remove.unshift(orgWrite)
    }
  }
  return (await allUcanAuth<HookContext>(ucanArgs, {
    loginPass: [[['owner'], '*']],
    adminPass: ['patch', 'create', 'remove'],
    cap_subjects,
    or: '*'
  })(context)) as any
}

const addKey = async (context: HookContext): Promise<HookContext> => {
  const org = await new CoreCall('orgs', context, { skipJoins: true }).get(context.data.org, {
    admin_pass: true
  })
  const { name } = org
  context.data.key =
    name.toLowerCase().split(' ').join('_') + ':' + context.data.name.toLowerCase().split(' ').join('_')
  return context
}


const scrubFields = ['terms', 'bonus.*.terms', 'ad']
// A configure function that registers the service and its hooks via `app.configure`
export const comps = (app: Application) => {
  // Register our service on the Feathers application
  app.use(compsPath, new CompsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: compsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(compsPath).hooks({
    around: {
      all: [schemaHooks.resolveExternal(compsExternalResolver), schemaHooks.resolveResult(compsResolver)]
    },
    before: {
      all: [
        authenticate,
        logChange(),
        schemaHooks.validateQuery(compsQueryValidator),
        schemaHooks.resolveQuery(compsQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        addKey,
        schemaHooks.validateData(compsDataValidator),
        schemaHooks.resolveData(compsDataResolver),
        scrub(scrubFields)
      ],
      patch: [
        schemaHooks.validateData(compsPatchValidator),
        schemaHooks.resolveData(compsPatchResolver),
        scrub(scrubFields)
      ],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [compsPath]: CompsService
  }
}
