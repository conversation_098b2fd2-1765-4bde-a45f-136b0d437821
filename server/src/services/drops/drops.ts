// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    dropsDataValidator,
    dropsPatchValidator,
    dropsQueryValidator,
    dropsResolver,
    dropsExternalResolver,
    dropsDataResolver,
    dropsPatchResolver,
    dropsQueryResolver
} from './drops.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {DropsService, getOptions} from './drops.class.js'
import {dropsPath, dropsMethods} from './drops.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts} from 'feathers-ucan';
import {logChange, logHistory} from '../../../utils/change-log.js';
import {scrub} from '../../../utils/sanitize/index.js';
import {handleVotes} from '../../utils/votes/index.js';

export * from './drops.class.js'
export * from './drops.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['drops', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['drops', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        creatorPass: ['*'],
        adminPass: ['patch', 'create', 'remove']
    })(context) as any;
}

// A configure function that registers the service and its hooks via `app.configure`
export const drops = (app: Application) => {
    // Register our service on the Feathers application
    app.use(dropsPath, new DropsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: dropsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(dropsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(dropsExternalResolver),
                schemaHooks.resolveResult(dropsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(dropsQueryValidator),
                schemaHooks.resolveQuery(dropsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(dropsDataValidator),
                schemaHooks.resolveData(dropsDataResolver),
                scrub(['body']),
            ],
            patch: [
                schemaHooks.validateData(dropsPatchValidator),
                schemaHooks.resolveData(dropsPatchResolver),
                scrub(['body']),
                logHistory(['body', 'title']),
                handleVotes()
            ],
            remove: []
        },
        after: {
            all: [
                handleVotes()
            ],
            patch: [handleVotes()]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations' {
    interface ServiceTypes {
        [dropsPath]: DropsService
    }
}
