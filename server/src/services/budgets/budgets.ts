// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    budgetsDataValidator,
    budgetsPatchValidator,
    budgetsQueryValidator,
    budgetsResolver,
    budgetsExternalResolver,
    budgetsDataResolver,
    budgetsPatchResolver,
    budgetsQueryResolver
} from './budgets.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {BudgetsService, getOptions} from './budgets.class.js'
import {budgetsPath, budgetsMethods} from './budgets.shared.js'
import {
    allUcanAuth,
    anyAuth,
    CapabilityParts,
    CoreCall,
    loadExists,
    setExists,
    checkUcan
} from 'feathers-ucan';
import {getJoin} from '../../../utils/fast-join.js';
import {logChange, logHistory} from '../../../utils/change-log.js';
import {relate} from '../../../utils/relate/index.js';

export * from './budgets.class.js'
export * from './budgets.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['budgets', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['budgets', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        get: writer,
        find: anyAuth,
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []
    if (!['find', 'create'].includes(context.method) && !context.params.admin_pass && !context.params.loopingAuth) {
        const existing = await loadExists(context, {params: {admin_pass: true, loopingAuth: true}});
        context = setExists(context, existing);
        //allow changes before approval
        if (existing) {
            const orgNamespace = `orgs:${existing.owner}`;
            cap_subjects.push(existing.owner)
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'providerAdmin']]
            for (const w of orgWrite) {
                ucanArgs.get.unshift(w);
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['managers/owner'], '*']],
        cap_subjects
    })(context) as any;
}

const filterResult = async (context: HookContext) => {
    if(context.params.skip_hooks) return context;
    const d = context.result.data || []
    const data:any = []
    const l = context.params.login.owner;
    const ownerPass:any = [];
    for(let i = 0; i < d.length; i++){
        if(d[i].members && d[i].members.includes(l)) data.push(d[i])
        else if(d[i].managers && d[i].managers.includes(l)) data.push(d[i]);
        else if(d[i].owner) {
            if (ownerPass.includes(d[i].owner)) {
                data.push(d[i])
            } else {
                const orgNamespace = `orgs:${d[i].owner}`;
                const canEdit: Array<CapabilityParts> = [['budgets', 'WRITE'], [orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'financeAdmin'], [orgNamespace, 'providerAdmin']] as Array<CapabilityParts>;
                const ctx = await checkUcan({find: canEdit} as any, {noThrow: true})(context)
                if (ctx.params.canU) {
                    data.push(d[i]);
                    ownerPass.push(d[i].owner)
                }
            }
        }
    }
    context.result.data = data;
    return context;
}

const relateOwner = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'owner',
        therePath: 'budgets',
        thereService: 'orgs',
        paramsName: 'budgetOwner'
    })(context)
}
const relateParent = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'parent',
        therePath: 'children',
        thereService: 'budgets',
        paramsName: 'budgetParent'
    })(context)
}

const relateFa = async (context: HookContext) => {
    if (context.result?.careAccount) return relate('otm', {
        herePath: 'careAccount',
        therePath: 'budgets',
        thereService: 'care-accounts',
        paramsName: 'budgetCareAccount'
    })(context);
    return context;
}

const limitLayers = async (context: HookContext) => {
    if (context.data.parent) {
        const parent = await new CoreCall('budgets', context, {skipJoins: true}).get(context.data.parent)
        if (parent.parent) throw new Error('Budgets can only be 2 layers deep - use categories instead');
    } else if (!context.data.careAccount) throw new Error('Budgets must have either a care account or a parent budget');
    return context;
}

const limitParent = async (context: HookContext) => {
    const { parent, $set } = context.data;
    const newParent = parent || $set?.parent;
    if(newParent && String(newParent) === String(context.id)) throw new Error('Cannot assign budget as its own parent');
    return context;
}

const runJoins = async (context: HookContext) => {
    if(context.params.skip_hooks || context.params.exists_check) return context;
    const {budget_owner, all_parents, budget_parent} = context.params.runJoin || {}
    if (budget_owner) return getJoin({herePath: 'owner', service: 'orgs'})(context)
    if(budget_parent){
        const obj = context.result.parent ? { herePath: 'parent', service: 'budgets' } : { herePath: 'careAccount', service: 'care-accounts'}
        return getJoin(obj)(context)
    }
    if(all_parents) {
        const obj = context.result.parent ? { herePath: 'parent', service: 'budgets', params: { runJoin: { all_parents: true }}} : { herePath: 'careAccount', service: 'care-accounts'}
        return getJoin(obj)(context)
    }
    return context;
}

import {
    parentOrCareAccount,
    looseRelateUsers,
    handleRemove,
    cascadeTotals,
    cascadeSpent,
    cascadeMcc
} from './hooks/index.js';

import {checkFreeze} from '../care-accounts/care-accounts.js';
// A configure function that registers the service and its hooks via `app.configure`

const checkMerchantCategories = async (context: HookContext) => {
    //before adding to whitelist or removing blacklist - need to ensure proper syncing with parent
    const {mcc_whitelist, mcc_blacklist, $addToSet, $pull} = context.data;
    if (mcc_whitelist) delete context.data.mcc_whitelist;
    if (mcc_blacklist) delete context.data.mcc_blacklist;
    const addWl = $addToSet?.mcc_whitelist;
    const pullBl = $pull?.mcc_blacklist;
    if (addWl || pullBl) {
        const existing = await loadExists(context);
        context = setExists(context, existing);
        const config = existing.careAccount ? { service: 'care-accounts', path: 'careAccount', name: 'Care Account' } : { service: 'budgets', path: 'parent', name: 'Parent Budget' }
        const parent = await new CoreCall(config.service, context).get(existing[config.path], { admin_pass: true })
        const { mcc_whitelist:parent_wl, mcc_blacklist:parent_bl } = parent || {}
        if(addWl){
            const add = addWl.$each ? addWl.$each : [addWl];
            for(let i = 0; i < add.length; i++){
                if((parent_wl?.length && !parent_wl.includes(add[i])) || parent_bl?.includes(add[i])) throw new Error(`${config.name} for this budget restricts spend categories and ${add[i]} is not enabled`)
            }
        }
        if(pullBl){
            const pull = Array.isArray(pullBl)? pullBl:[pullBl];
            for(let i = 0; i < pull.length; i++){
                if (parent_bl?.length && parent_bl.includes(pull[i])) throw new Error(`${config.name} for this budget restricts category ${pull[i]}. Remove from ${config.name} before removing from card restricted categories.`)
            }
        }
    }
}
export const budgets = (app: Application) => {
    // Register our service on the Feathers application
    app.use(budgetsPath, new BudgetsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: budgetsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(budgetsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(budgetsExternalResolver),
                schemaHooks.resolveResult(budgetsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(budgetsQueryValidator),
                schemaHooks.resolveQuery(budgetsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(budgetsDataValidator),
                schemaHooks.resolveData(budgetsDataResolver),
                limitLayers,
                relateParent,
                relateOwner,
                relateFa
            ],
            patch: [
                schemaHooks.validateData(budgetsPatchValidator),
                schemaHooks.resolveData(budgetsPatchResolver),
                limitParent,
                parentOrCareAccount,
                cascadeTotals(),
                cascadeSpent(),
                cascadeMcc('budgets'),
                relateParent,
                relateOwner,
                relateFa,
                logHistory(['managers', 'approvers', 'members', 'parent', 'children', 'recurs', 'amount'])
            ],
            remove: [relateParent, relateOwner, relateFa]
        },
        after: {
            all: [runJoins],
            get: [
                // checkFreeze('budgets')
            ],
            find: [
                filterResult
            ],
            create: [
                relateParent,
                relateOwner,
                relateFa,
                looseRelateUsers('budget_user')
            ],
            patch: [
                checkFreeze('budgets'),
                handleRemove,
                relateParent,
                relateOwner,
                relateFa,
                looseRelateUsers('budget_user')
            ],
            remove: [handleRemove, relateParent, relateOwner, relateFa, looseRelateUsers('budget_user')]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [budgetsPath]: BudgetsService
    }
}
