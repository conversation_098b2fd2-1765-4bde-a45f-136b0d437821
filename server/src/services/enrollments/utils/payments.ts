import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';
import {ObjectId} from 'mongodb';
import {_get} from '../../../../utils/dash-utils.js';

const getSyncedCoverages = (enrollment: any) => {
    return async (context: HookContext): Promise<HookContext> => {

        if(context.params.skip_hooks) return context;
        if (enrollment.claimPayments?.length) {
            const totals = {
                payments_amount: {$sum: 'amount'},
                payments_coins: {$sum: 'coins'},
                payments_ded: {$sum: 'ded'},
                payments_refunded: {$sum: '$refund_amount'},
                payments_coins_refunded: {$sum: '$refund_coins'},
                payments_ded_refunded: {$sum: '$refund_ded'}
            }
            const enrollmentId = typeof enrollment._id === 'string' ? ObjectId.createFromHexString(enrollment._id) : enrollment._id;
            const payments = await new CoreCall('claim-payments', context)._find({
                skip_hooks: true, admin_pass: true,
                query: {
                    enrollment: enrollmentId,
                    status: {$ne: 'cancelled'},
                    terminated: {$exists: false},
                    deleted: { $ne: true }
                },
                paginate: false,
                pipeline: [
                    //unwind refunds to reduce amount by all refunds with matching status
                    {
                        $unwind: 'refunds'
                    },
                    //only reduce completed refunds
                    {
                        $match: {
                            'refunds.status': 'complete'
                        }
                    },
                    //regroup by _id to total all refunds per _id
                    {
                        $group: {
                            _id: '$_id',
                            refund_coins: {$sum: '$refunds.coins'},
                            refund_ded: {$sum: '$refunds.ded'},
                            refund_amount: {$sum: '$refunds.amount'}
                        }
                    },
                    //group by patient, coverage, and status for totaling
                    {
                        $group: {
                            _id: {
                                patient: '$patient',
                                coverage: '$coverage',
                                status: '$status'
                            }
                        }
                    },
                    {
                        $group: {
                            _id: {
                                patient: '$_id.patient',
                                coverage: '$_id.coverage',
                            },
                            statuses: {
                                $push: {
                                    status: '$_id.status',
                                    ...totals
                                }
                            }
                        }
                    },
                    //group create an array for each coverage total that totals by status
                    {
                        $group: {
                            _id: '$_id.patient',
                            statuses: '$statuses',
                            coverages: {
                                $push: {
                                    coverage: "$_id.coverage",
                                    statuses: '$statuses'
                                }
                            }
                        }
                    }
                ]
            })
            //payments structure
            // {
            //     "_id": "patientId_1",
            //     "coverages": [
            //     {
            //         "coverage": "coverageId_1",
            //         "statuses": [
            //             { "status": "active", ...paids[status] },
            //             { "status": "inactive", ...paids[status] }
            //         ],
            //
            //     },
            //
            // ],
            if (payments.length) {
                const patientClaims = enrollment.patientClaims || {}
                const patchObj: any = {}
                const byCoverage: any = {}
                let run;
                const getTotalObj = (obj: any, ex: any) => {
                    if (obj) {
                        const amount = (obj.payments_amount || 0) - (obj.payments_refunded || 0);
                        const ded = (obj.payments_ded || 0) - (obj.payments_ded_refunded || 0)
                        const coins = (obj.payments_coins || 0) - (obj.payments_coins_refunded || 0)
                        const {
                            ded: cDed,
                            coins: cCoins,
                            amount: cAmount
                        } = ex || {}
                        if (amount !== cAmount || ded !== cDed || coins !== cCoins) {
                            run = true;
                            return {
                                amount,
                                ded,
                                coins
                            }
                        } else return undefined
                    } else return undefined
                }
                const setOne = (patientId: string, coverage: any, status: string) => {
                    const statuses = coverage.statuses || [];
                    //get relevant status based totals for this coverage
                    const res = statuses.filter(a => a.status === status)[0];
                    const totalObj = getTotalObj(res, _get(patientClaims, [patientId, coverage.coverage, status]));
                    if (totalObj) {
                        //set patient claims totals for this coverage
                        patchObj[`patientClaims.${patientId}.${coverage.coverage}.${status}`] = totalObj
                        const {ded, coins, amount} = (byCoverage[coverage.coverage] || {})[status] || {ded: 0};
                        byCoverage[coverage.coverage] = {
                            ...byCoverage[coverage.coverage],
                            [status]: {ded: (ded || 0) + totalObj.ded, coins: (coins || 0) + totalObj.coins, amount: (amount || 0) + totalObj.amount},
                        }
                    }
                }
                const statusKeys = ['paid', 'pending', 'request', 'offer']

                const setCoverage = (patientObj) => {
                    for (let i = 0; i < (patientObj.coverages || []).length; i++) {
                        //iterate through coverages and status keys to set patientClaims for each patient and each coverage
                        for (let s = 0; s < statusKeys.length; s++) {
                            setOne(String(patientObj._id), patientObj.coverages[i], statusKeys[s])
                        }
                    }
                }
                //iterate through each claim payment - which is now a patient object with totals for each patient by status and by coverage - by status
                for (let c = 0; c < payments.length; c++) {
                    //set patient and coverageClaims status objects
                    setCoverage(payments[c]);
                    // //also iterate through and set patientClaims total by patient
                    // for (let i = 0; i < (payments[c].statuses || []).length; i++) {
                    //     const status = payments[c].statuses[i]
                    //     if (status) {
                    //         const res = getTotalObj(status, _get(patientClaims, status.status))
                    //         if (res) patchObj[`patientClaims.${status.status}`] = res
                    //     }
                    // }
                }

                if (run) {
                    patchObj.coverageClaims = byCoverage
                    return await new CoreCall('enrollments', context)._patch(enrollment._id, {$set: patchObj}, { skip_hooks: true, admin_pass: true})
                        .catch(err => {
                            console.error(`Could not patch enrollment ${enrollment._id} in syncing coverage totals: ${err.message}`)
                            return enrollment;
                        })
                }
            }
        }
        return enrollment;
    }
}
export const syncCoveragePayments = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;
    const {claimPayments} = context.data.$addToSet || {};
    if (claimPayments) {
        context.result = await getSyncedCoverages(context.result)(context);
    }
    return context;
}
export const reSyncCoveragePayments = async (context: HookContext): Promise<HookContext> => {
    if (context.params.banking?.sync_claims_payments) {
        if (context.method === 'find') {
            if (context.result.total) {
                const runOne = async (claim: any) => {
                    return await getSyncedCoverages(claim)(context)
                }
                context.result.data = await Promise.all(context.result.data.map(a => runOne(a)))
            }
        } else {
            context.result = await getSyncedCoverages(context.result)(context);
        }
    }
    return context;
}
