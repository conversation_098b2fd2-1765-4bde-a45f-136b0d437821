// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    enrollmentsDataValidator,
    enrollmentsPatchValidator,
    enrollmentsQueryValidator,
    enrollmentsResolver,
    enrollmentsExternalResolver,
    enrollmentsDataResolver,
    enrollmentsPatchResolver,
    enrollmentsQueryResolver
} from './enrollments.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {EnrollmentsService, getOptions} from './enrollments.class.js'
import {enrollmentsPath, enrollmentsMethods} from './enrollments.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, getExists, loadExists, setExists} from 'feathers-ucan';
import {getJoin} from '../../utils/fast-join.js';
import {logChange} from '../../utils/change-log.js';
import {ping} from '../../utils/notifications/index.js';
import {_get, _pick} from '../../utils/dash-utils.js';
import {AnyObj} from '../../utils/types.js';
import {limitData} from '../../utils/common/checks.js';
import {handleDeepEncrypt} from '../../utils/encryption/index.js';
import {getStateCode} from '../../utils/common/states.js';

export * from './enrollments.class.js'
export * from './enrollments.schema.js'

import {genPingArgs} from './config/index.js';

const sendPings = async (context: HookContext) => {
    const config = await genPingArgs({})(context);
    if (context.type === 'after' && Array.isArray(context.result)) {
        const fullPlan = await new CoreCall('plans', context, {skipJoins: true}).get(context.result.slice(0, 5).filter(a => !!a?.plan)[0].plan)
            .catch(err => {
                console.error(`Error getting full plan for ping config`, err.message);
                return {name: 'your group health plan'}
            })
        const sendOne = async (res: any) => {
            const modifiedConfig = await genPingArgs({id: res._id, planName: fullPlan.name})(context)
            return await ping(modifiedConfig, {result: res})(context)
                .catch(err => {
                    console.log(`Error pinging. ${err.message}: Config ${modifiedConfig}`)
                })
        }
        await Promise.all(context.result.map(a => sendOne(a)))
            .catch(err => {
                console.error(`Failed to send ping on enrollment create: ${err.message}`);
                return undefined
            })
        return context;
    } else {
        return await ping(config)(context)
            .catch(err => {
                console.error(`Failed to send ping on enrollment create: ${err.message}`);
            });
    }
}

const authenticate = async (context: HookContext) => {
    const writer = [['enrollments', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['enrollments', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: writer,
        patch: writer,
        update: writer,
        remove: deleter
    };

    const cap_subjects: any = []

    let existing = {plan: undefined, org: undefined};
    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {
            existing = await loadExists(context);
            context = setExists(context, existing);
        }
        const orgId = existing?.org || context.data.org;
        const planId = existing?.plan || context.data.plan;
        const orgNamespace = `orgs:${orgId}`;
        cap_subjects.push(orgId);
        cap_subjects.push(planId);
        const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [`plans:${planId}`, 'planAdmin']]
        for (const w of orgWrite) {
            ucanArgs.patch.unshift(w);
            ucanArgs.update.unshift(w);
            ucanArgs.remove.unshift(w);
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        loginPass: [[['person/owner'], '*']],
        adminPass: ['patch', 'create', 'remove'],
        or: '*',
        cap_subjects
    })(context) as any;
}

const getZipPoint = async (zip: string, context: HookContext) => {
    if (!zip || zip.length !== 5) return undefined
    const drawer = await new CoreCall('junk-drawers', context).find({
        query: {
            itemId: `zips|${zip.slice(0, 3)}`,
            $limit: 1
        }
    })
    return drawer.data[0]?.data[zip]?.lngLat
}

const enrolledPoints = async (context: HookContext): Promise<HookContext> => {
    if (Array.isArray(context.data)) return context;
    const data = context.data
    const {$set} = data;
    let obj: any = {};
    const run: Array<string> = [];
    if ($set) {
        obj = {...$set}
        for (const k in $set) {
            if (/enrolled./.test(k) && (k.split('.').length === 2 || /.zip/.test(k))) run.push(k);
        }
    }
    if (run.length && !context.params.skip_zip_points) {
        for (let i = 0; i < run.length; i++) {
            const k = run[i];
            const spl = k.split('.');
            //if zip is being set
            if (/.zip/.test(k)) {
                const zip = obj[k];
                const point = await getZipPoint(zip, context);
                if (point) obj[`${spl.slice(0, spl.length - 1).join('.')}.point`] = point;
            } else {
                //if enrolled.id is being set
                const zip = obj[k].zip;
                const point = await getZipPoint(zip, context);
                if (point) obj[k].point = point;
            }
            data.$set = obj;
        }
    }
    context.data = data;
    return context;
}

const runJoins = async (context: HookContext): Promise<HookContext> => {
    const {enrollment_person, enrollee_points, enrollment_rates} = context.params.runJoin || {};
    if (enrollment_rates) {
        const covIds = Object.keys(context.result.coverages || {})
        const rates = await new CoreCall('rates', context).find({
            query: {
                coverage: {$in: covIds},
                state: getStateCode(context.result.address?.region),
                $limit: covIds.length
            }
        });
        context.result._fastjoin = {...context.result._fastjoin, rates: rates.data}
    }
    if (enrollee_points) {
        const {enrolled} = context.result;
        const $set = {};
        for (const e in enrolled || {}) {
            if (enrolled[e].zip && !enrolled[e].point) {
                const point = await getZipPoint(enrolled[e].zip, context);
                if (point) $set[`enrolled.${e}.point`] = point;
                context.result.enrolled[e].point = point;
            }
        }
        if (Object.keys($set).length) await new CoreCall('enrollments', context).patch(context.result._id, {$set}, {skip_zip_points: true, skip_hooks: true, admin_pass: true})
    }
    if (enrollment_person) return getJoin({
        service: 'ppls',
        herePath: 'person',
        params: {runJoin: context.params.runJoin}
    })(context)

    return context;
}

export const getIdempotencyKey = (e: { person: string, plan: string, version: string } & any) => {
    return `${e.person}|${e.plan}|${e.version}`
}
const handleMassCreate = async (context: HookContext) => {
    if (context.params._from_plan && Array.isArray(context.data)) {

        const by_key: any = {};

        for (const er of context.data) {
            by_key[er.idempotency_key] = er;
        }

        //remove any duplicates so no error is thrown for unique index
        const existing = await new CoreCall('enrollments', context, {skipJoins: true}).find({
            query: {
                $limit: context.data.length,
                deleted: {$ne: true},
                idempotency_key: {$in: Object.keys(by_key)}
            }
        })
            .catch(err => {
                console.error(`Error finding existing enrollments for mass create for planId ${context.data[0].plan}: ${err.message}`)
                return {data: []}
            })
        //sort ascending and the existing list descending so we have no index issues and can map just once instead of re-mapping every item in the array for each transaction - trying to be efficient
        for (const e of existing.data || []) {
            delete by_key[e.idempotency_key]
        }

        context.data = Object.values(by_key);

    }
    return context;
}

const updateAges = async (result: any, context: HookContext) => {
    let pplIds: any[] = [];
    for (const cvgId in result.coverages || {}) {
        for (const id of result.coverages[cvgId]?.participants || []) {
            if (!pplIds.includes(id)) pplIds.push(id);
        }
    }
    if (pplIds.length) {
        const ppls = await new CoreCall('ppls', context, {skipJoins: true}).find({
            query: {
                _id: {$in: pplIds},
                $limit: pplIds.length
            }, admin_pass: true
        })
            .catch(err => {
                console.error(`Error updating enrollment ages for enrollment id ${result._id}: ${err.message}`)
                throw new Error(`Could not complete enrollment - there was an error with retrieving participant ages: ${err.message}`)
            });
        if (ppls.data) {
            const obj = {};
            for (const person of ppls.data) {
                if (!person.dob) throw new Error(`Improper date of birth set for household member: ${person.name}`)
                const today = new Date();
                const birthDate = new Date(person.dob);
                let age = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();

                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }
                const {lastName, firstName, gender, ssn, dob} = person;
                obj[person._id] = {age, lastName, firstName, gender, ssn, dob}
            }
            result.enrolled = obj;
        }
    }
    return result;
}

import {totalIncome} from '../households/utils/incomes.js';

const relateEnrolled = async (context: HookContext): Promise<HookContext> => {
    if (context.params.skip_hooks) return context;
    if (context.type === 'before') {
        const status = context.data?.status || context.data?.$set?.status;
        const runReview = status === 'complete' || context.params.runJoin?.plan_enrollment_update
        const otherRun = () => {
            return Object.keys({...context.data, ...context.data.$set}).some(a => /^coverages\.[^.]+\.premium$/.test(a))
        }
        if (!runReview && !otherRun()) return context;
        if (status) {
            context.data.status = status;
            if(status === 'complete'){
                const exists = await loadExists(context);
                context = setExists(context, exists);
                const address = context.data.address || exists.address;
                const county = context.data.county || exists.county;
                if (!address?.postal || !county?.fips) throw new Error('Enrollment address required to complete enrollment')
            }
        }
        if (context.data.$set?.status) delete context.data.$set.status;
        if (runReview && (context.data.cafe || context.data.coverages || (context.data.$set && Object.keys(context.data.$set).some(a => ['cafe', 'coverages'].includes(a.split('.')[0]))))) throw new Error('Cannot update cafeteria or coverages when marking enrollment complete');

        context.params.plan_enrollment_update = runReview ? 'full' : 'premium';

        return context;
    } else {
        const {plan_enrollment_update} = context.params;
        if (plan_enrollment_update) {

            /** Get updated enrollment contributions and details based on the saved changes */

            const {contributions: cont, patchObj} = await getEnrollmentContributions(context.result)(context)

            const {enrolled} = await updateAges(context.result, context);
            patchObj.enrolled = enrolled;
            patchObj.enrolledAt = new Date();
            patchObj.hasFinished = context.result.status === 'complete'

            if (plan_enrollment_update === 'full') {
                const total = await totalIncome(context.result)(context);
                if (total) patchObj.householdIncome = total
                for (const k in context.result.coverages || {}) {
                    if ((context.result.coverages[k].policy || context.result.coverages[k].individual_coverage) && !context.result.coverages[k].confirmedAt) {
                        patchObj.status = 'review'
                        patchObj.statusNote = 'Individual Coverage Review'
                    }
                }
                let match = true;
                let msg = patchObj.statusNote || '';
                if (Math.abs(cont.needed.preTax - cont.employee.preTax) > .01) {
                    match = false;
                    msg += (msg ? ' | ' : '') + 'Pre-tax contributions don\'t match elected costs.'
                }
                if (Math.abs(cont.needed.postTax - cont.employee.postTax) > .01) {
                    match = false;
                    msg += (msg ? ' | ' : '') + 'Post-tax contributions don\'t match elected costs.'
                }
                if (!match) {
                    patchObj.status = 'review'
                    patchObj.statusNote = msg
                }

            }

            context.result = await new CoreCall('enrollments', context).patch(context.result._id, {$set: patchObj}, {
                skip_hooks: true,
                admin_pass: true
            })
                .catch(err => console.log(`Failed to patch enrollment with calculated contributions: ${err.message}`))

            /** patch the plan so it can run updates */
            const planPatch: any = {
                $set: {
                    [`enrollments.${context.result.version}.lastEnrolled`]: new Date(),
                    [`enrollments.${context.result.version}.lastUpdate`]: new Date()
                }
            }
            await new CoreCall('plans', context, {skipJoins: true}).patch(context.result.plan, planPatch, {special_change: []})
                .catch(err => console.error(`Error incrementing enrolled for plan ${context.result.plan} - enrollment ${context.result._id}: ${err.message}`));

        }
    }
    return context;
}

const guardComplete = async (context: HookContext): Promise<HookContext> => {
    if (!context.params.special_change) {
        const exists = await loadExists(context);
        const closed = new Date(exists.close).getTime() < new Date().getTime()
        if (exists.status === 'complete' && closed) {
            const {$set} = context.data;
            const newSet = {};
            let pass;
            for (const k in $set || {}) {
                if (k === 'cafe.hsa.amount') {
                    pass = true;
                    newSet[k] = $set[k];
                }
            }
            if (pass) {
                const data = {$set: newSet}
                context.data = limitData(context.data, data);
            } else throw new Error('This enrollment is already complete, you cannot change its status.')
        } else if (exists.status === 'closed') throw new Error('This enrollment is closed')
        else {
            if (new Date(exists.close).getTime() < new Date().getTime()) {
                context.data = {status: 'closed'}
            }
        }
        context = setExists(context, exists);
    }
    return context;
}

const specialEnrollment = async (context: HookContext): Promise<HookContext> => {
    if (context.data._special) {
        const {version, org, plan} = context.data;
        const errs: any[] = [];
        const make = async (er) => {
            const data = {version, org, plan, ...er}
            await new CoreCall('enrollments', context, {skipJoins: true}).create(data)
                .catch(err => {
                    errs.push({...data, ...err})
                })
        }

        const participants = await Promise.all(context.data.participants.map(a => make(a)));
        context.result = {_id: '*', ...context.data, participants}
    }
    return context;
}

const logBenefitChanges = async (context: HookContext): Promise<HookContext> => {
    const {$set, cafe} = context.data;
    const trackPaths = ['amount', 'optOut'];
    if (cafe) {
        const paths: string[] = [];
        for (const k in cafe) {
            for (const subK in cafe[k]) {
                if (trackPaths.includes(subK)) paths.push(`cafe.${k}.${subK}`)
            }
        }
        if (paths.length) {
            const existing = await loadExists(context);
            context = setExists(context, existing);
            for (const path of paths) {
                const ex = _get(existing, path) as AnyObj;
                if (ex !== _get(context.data, path)) {
                    const oneLess = path.split('.').slice(0, 2).join('.');
                    context.data.$set[`${oneLess}.updateHistory`] = [..._get(existing, `${oneLess}.updateHistory`, []) as Array<any>, {
                        ..._pick(ex, trackPaths),
                        date: new Date()
                    }]
                }
            }
        }
    }
    if ($set) {
        const paths: string[] = []
        for (const k in $set) {
            if (/cafe\./.test(k)) {
                paths.push(k)
            }
        }
        if (paths.length) {
            const existing = await loadExists(context);
            context = setExists(context, existing);
            for (const path of paths) {
                const ex = _get(existing, path) as AnyObj;
                if (ex !== context.data) {
                    const oneLess = path.split('.').slice(0, 2).join('.');
                    context.data.$set[`${oneLess}.updateHistory`] = [..._get(existing, `${oneLess}.updateHistory`, []) as Array<any>, {
                        ..._pick(ex, trackPaths),
                        date: new Date()
                    }]
                }
            }
        }
    }
    return context;
}
import {syncCoveragePayments, reSyncCoveragePayments} from './utils/payments.js';

const handleTermSingle = async (data: any, context: HookContext, exists: any): Promise<any> => {
    const terminated = data.terminated || data.$set?.terminated;
    if (terminated) {
        if (context.type === 'before') {
            if (!exists.terminated) {
                // TODO: create COBRA
                await new CoreCall('cobras', context).create({
                    enrollment: context.id,
                    participant: exists.participant,
                    household: exists.household,
                    deadline: new Date(new Date(data.terminatedAt).getTime() + (1000 * 60 * 60 * 24 * 60))
                })
            }
        } else {
            // For after hooks, we need to handle result data
            const result = Array.isArray(context.result) ? context.result.find(r => r._id === context.id) : context.result;
            if (result) context.params.terms_result = true;
        }
    }
    return data;
}

const handleTerm = async (context: HookContext): Promise<HookContext> => {
    // This hook needs special handling because it uses loadExists
    const exists = await loadExists(context);

    if (Array.isArray(context.data)) {
        context.data = await Promise.all(
            context.data.map(item => handleTermSingle(item, context, exists))
        );
    } else {
        context.data = await handleTermSingle(context.data, context, exists);
    }

    return context;
}

const handlePlanYear = (context: HookContext) => {
    if (context.data.version) {
        context.data.planYear = context.data.version.split('_')[0]
    }
    return context;
}
const postPlanYear = (context: HookContext) => {
    if(context.params.skip_hooks) return context;
    if (context.result.version && !context.result.planYear) {
        new CoreCall('enrollments', context)._patch(context.result._id, {planYear: context.result.version.split('_')[0]}, {
            skip_hooks: true,
            admin_pass: true
        })
            .catch(err => console.log(`Failed to add plan year from version on enrollment ${context.result._id}: ${err.message}`))
    }
    return context;
}

/** Ensure that only one of policy of individual_coverage is selected for a given coverage */
const oneOfPolicyOrCoverage = async (context: HookContext) => {
    if(context.params.one_of_loop || context.params.skip_hooks) return context;
    const patchObj: any = {};
    let runPatch = false;
    let ex = getExists(context);
    if (!ex) ex = {coverages: {}}
    for (const covId in context.result?.coverages || {}) {
        const cov = context.result.coverages[covId];
        const ov = ex.coverages[covId] || {};
        if (cov.policy && cov.individual_coverage) {
            if (cov.policy && ov.policy !== cov.policy) {
                runPatch = true;
                patchObj[`coverages.${covId}.policy`] = cov.policy;
                patchObj[`coverages.${covId}.fullPolicy`] = cov.fullPolicy;
                patchObj[`coverages.${covId}.aptc`] = 0;
                // Remove the conflicting property
                if (!patchObj.$unset) patchObj.$unset = {};
                patchObj.$unset[`coverages.${covId}.individual_coverage`] = '';
                patchObj.$unset[`coverages.${covId}.fullCoverage`] = '';
            } else if (cov.individual_coverage && ov.individual_coverage !== cov.individual_coverage) {
                runPatch = true;
                patchObj[`coverages.${covId}.individual_coverage`] = cov.individual_coverage;
                patchObj[`coverages.${covId}.fullCoverage`] = cov.fullCoverage;
                // Remove the conflicting property
                if (!patchObj.$unset) patchObj.$unset = {};
                patchObj.$unset[`coverages.${covId}.policy`] = '';
                patchObj.$unset[`coverages.${covId}.fullPolicy`] = '';
                patchObj.$unset[`coverages.${covId}.aptc`] = '';
            }
        }
    }
    if (runPatch) {
        context.params.one_of_loop = true;
        context.result = await new CoreCall('enrollments', context)._patch(context.result._id, patchObj, {
            skip_hooks: true,
            admin_pass: true
        })
            .catch(err => console.log(`Failed to patch enrollment with policy or individual_coverage: ${err.message}`))
    }
    return context;
}

const sendReminder = async (context: HookContext) => {
    if (context.params.runJoin?.enroll_reminder) {
        if (['complete', 'closed', 'review'].includes(context.result.status)) return context;
        await sendPings(context);
    }
    return context;
}

const setIdempotency = (context: HookContext) => {
    if (Array.isArray(context.data)) {
        context.data = context.data.map(a => {
            a.idempotency_key = getIdempotencyKey(a);
            return a;
        })
    } else {
        context.data.idempotency_key = getIdempotencyKey(context.data);
    }
    return context;
}

const syncCoverageTypes = async (context: HookContext) => {
    const ex = await loadExists(context);
    const {coverages} = context.result || {};
    const findIds: any = []
    for (const k in coverages) {
        if (!coverages[k].coverageType || typeof coverages[k].coverageType !== 'boolean') findIds.push(k);
    }
    if (findIds.length) {
        const cvgs = await new CoreCall('coverages', context).find({
            query: {
                $limit: findIds.length,
                _id: {$in: findIds}
            }
        })
            .catch(err => {
                console.log(`Error getting coverages in enrollment sync coverage types: ${err.message}`)
                return {data: []}
            })
        const patchObj: any = {};
        for (const cov of cvgs.data) {
            context.result.coverages[cov._id].coverageType = cov.type;
            context.result.coverages[cov._id].postTax = !!cov.postTax;
            patchObj[`coverages.${cov._id}.postTax`] = !!cov.postTax;
            patchObj[`coverages.${cov._id}.coverageType`] = cov.type;
        }
        await new CoreCall('enrollments', context).patch(context.result._id, {$set: patchObj}, {
            skip_hooks: true,
            admin_pass: true
        })
            .catch(err => console.log(`Error patching enrollment with coverage types: ${err.message}`))
    }
    return context;
}

const syncContributions = async (context: HookContext) => {
    if(context.params.skip_hooks) return context;
    const ex = getExists(context);
    let changedElections = false;
    const {cafe, coverages} = context.result || {}
    const {cafe: exCafe, coverages: exCoverages} = ex || {}
    for (const k in cafe || {}) {
        if (!['pop', 'cash'].includes(k)) continue;
        const amount = cafe[k].amount || 0;
        const exAmount = exCafe[k]?.amount || 0;
        if (amount !== exAmount) {
            changedElections = true;
        }
    }
    for (const k in coverages || {}) {
        const premium = coverages[k].premium || (coverages[k].fullPolicy?.premium - (coverages[k].aptc?.aptc || 0)) || coverages[k].fullCoverage?.premium || 0;
        const exConfig = exCoverages[k] || {};
        const exPremium = exConfig.premium || (exConfig.fullPolicy?.premium - (exConfig.aptc?.aptc || 0)) || exConfig.fullCoverage?.premium || 0;
        if (premium !== exPremium) {
            changedElections = true;
        }
    }
    if (changedElections) {
        try {
            const {patchObj} = await getEnrollmentContributions(context.result)(context)
            await new CoreCall('enrollments', context).patch(context.result._id, {$set: patchObj}, {
                skip_hooks: true,
                admin_pass: true
            })
                .catch(err => console.log(`Error patching enrollment with calculated cafeteria contributions. Enrollment id ${context.result._id}. Err: ${err.message}`))
        } catch (e: any) {
            console.log(`Error calculating contributions on enrollment update: ${e.message}`)
        }

    }
    return context;

}

// A configure function that registers the service and its hooks via `app.configure`
export const enrollments = (app: Application) => {
    // Register our service on the Feathers application
    app.use(enrollmentsPath, new EnrollmentsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: enrollmentsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(enrollmentsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(enrollmentsExternalResolver),
                schemaHooks.resolveResult(enrollmentsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(enrollmentsQueryValidator),
                schemaHooks.resolveQuery(enrollmentsQueryResolver),
                handleDeepEncrypt('members', ['monthsSinceSmoked', 'disabled', 'incarcerated', 'dob', 'ssn', 'annualIncome'])
            ],
            find: [],
            get: [],
            create: [
                setIdempotency,
                specialEnrollment,
                handleMassCreate,
                handlePlanYear,
                schemaHooks.validateData(enrollmentsDataValidator),
                schemaHooks.resolveData(enrollmentsDataResolver),
                sendPings
            ],
            patch: [
                guardComplete,
                schemaHooks.validateData(enrollmentsPatchValidator),
                schemaHooks.resolveData(enrollmentsPatchResolver),
                relateEnrolled,
                handleTerm,
                logBenefitChanges,
                enrolledPoints
            ],
            remove: []
        },
        after: {
            all: [runJoins, handleDeepEncrypt('members', ['monthsSinceSmoked', 'disabled', 'incarcerated', 'dob', 'ssn', 'annualIncome'])],
            get: [reSyncCoveragePayments],
            find: [reSyncCoveragePayments],
            create: [sendPings, postPlanYear],
            patch: [
                relateEnrolled,
                handleTerm,
                syncContributions,
                oneOfPolicyOrCoverage,
                syncCoveragePayments,
                postPlanYear,
                sendReminder
            ],
            remove: [],
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [enrollmentsPath]: EnrollmentsService
    }
}
