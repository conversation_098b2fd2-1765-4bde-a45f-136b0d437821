// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type {
  Practitioners,
  PractitionersData,
  PractitionersPatch,
  PractitionersQuery
} from './practitioners.schema.js'

export type { Practitioners, PractitionersData, PractitionersPatch, PractitionersQuery }

export interface PractitionersParams extends MongoDBAdapterParams<PractitionersQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class PractitionersService<ServiceParams extends Params = PractitionersParams> extends MongoDBService<
  Practitioners,
  PractitionersData,
  PractitionersParams,
  PractitionersPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('practitioners'))
        .then((collection) => {
          collection.createIndex({npi: 1}, {unique: true});
          return collection
        })
  }
}
