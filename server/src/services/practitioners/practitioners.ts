// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    practitionersDataValidator,
    practitionersPatchValidator,
    practitionersQueryValidator,
    practitionersResolver,
    practitionersExternalResolver,
    practitionersDataResolver,
    practitionersPatchResolver,
    practitionersQueryResolver
} from './practitioners.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {PractitionersService, getOptions} from './practitioners.class.js'
import {practitionersPath, practitionersMethods} from './practitioners.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {logChange} from '../../utils/change-log.js';
import {_get} from '../../utils/dash-utils.js';
import {relate} from '../../utils/relate/index.js';
import axios from 'axios';
import { parsePhoneNumber } from 'awesome-phonenumber';
import {ObjectId} from 'mongodb';

export * from './practitioners.class.js'
export * from './practitioners.schema.js'

const $capFirst = (string:string):string => {
    return string ? string.charAt(0).toUpperCase() + string.slice(1) : '';
};

const searchApi = async (context: HookContext): Promise<HookContext> => {
    const {_search} = context.params;
    if (_search?.npi) {
        const diff = (context.params.query.$limit || 10) - context.result.total || 0
        if (diff > 0) {
            const getAtIdx = (val, path) => _get(val, path)

            const dataMap = async (val, idx) => {
                const addresses = getAtIdx(val, 'addresses') as Array<any>;
                if (addresses) {
                    const zips = addresses.map(a => a.postal_code?.slice(0, 5));
                    const $in = zips.filter(a => !!a).map(a => `zips|${a.slice(0, 3)}`)
                    const drawers = await new CoreCall('junk-drawers', context).find({
                        query: {
                            $limit: 1,
                            itemId: {$in}
                        }
                    })
                    if (drawers.total) {
                        const drawer = drawers.data[0];
                        for (const idx in zips) {
                            const zip = zips[idx];
                            if (!!zip) val.addresses[idx].lngLat = drawer.data[zip]?.lngLat
                        }
                    }
                }
                const getCities = (val) => {
                    const cities:string[] = [];
                    const ret: { city:string, state:string }[] = [];
                    for(const addr of (val.addresses || [])){
                        if(!cities.includes(addr.city)) {
                            cities.push(addr.city)
                            ret.push({ city: addr.city?.toLowerCase().split(' ').map(a => $capFirst(a)).join(' '), state: addr.state });
                        }
                    }
                    return ret;
                }
                const getLicenses = (val) => {
                    return (val.addresses || []).map(a => {
                        return {
                            license: a.license,
                            state: a.state,
                            desc: a.desc,
                            code: a.code
                        }
                    })
                }
                const getLicenseStates = (val) => {
                    return (val.addresses || []).map(a => a.state)
                }
                const getPhones = (val) => {
                    return (val.addresses || []).map(a => {
                        if(a.telephone_number) parsePhoneNumber(`+1${a.telephone_number.split('-').join('').split(' ').join('')}`)
                    }).filter(a => !!a);
                }
                const getName = (val, path) => {
                    const n = getAtIdx(val, `basic.${path}`) as string;
                    if(n) return n.toLowerCase().split(' ').map(a => $capFirst(a)).join(' ');
                    else return undefined;
                };
                return {
                    _id: `npi_${idx}`,
                    firstName: getName(val, 'first_name'),
                    lastName: getName(val, 'last_name'),
                    name_prefix: getName(val, 'name_prefix'),
                    credential: getAtIdx(val, 'basic.credential'),
                    soleProp: getAtIdx(val, 'basic.sole_proprietor'),
                    gender: getAtIdx(val, 'basic.gender'),
                    cities: getCities(val),
                    phones: getPhones(val),
                    npi_date: getAtIdx(val, 'basic.enumeration_date'),
                    npi_update: getAtIdx(val, 'basic.last_updated'),
                    npi_status: getAtIdx(val, 'basic.status'),
                    npi: getAtIdx(val, 'number'),
                    licenses: getLicenses(val),
                    taxonomy3: getAtIdx(val, 'taxonomies[0].desc'),
                    taxonomy1: getAtIdx(val, 'taxonomies[0].code'),
                    license_states: getLicenseStates(val),
                    auto_created: true
                }
            }
            const qMap = {
                type: (val) => `provider_type:${val || 'individual'}`,
                //Name is terms
                firstName: (val) => `first_name=${val}`,
                lastName: (val) => `last_name=${val}`,
                orgName: (val) => `organization_name=${val}`,
                city: (val) => `city=${val}`,
                zip: (val) => `postal_code=${val}`,
                license_state: (val) => `state=${val}`,
                code: (val) => `code=${val}`,
                $limit: (val) => `limit=${val}`,
                npi: (val) => `number=${val}`
            }
            const q = Object.keys(_search.npi || {}).map(a => qMap[a] ? qMap[a](_search.npi[a]) : undefined).filter(a => !!a).join('&')
            const res = await axios.get('https://npiregistry.cms.hhs.gov/api/?version=2.1&' + q)
                .catch(err => {
                    console.error(`Error searching practitioners`, err.message);
                    return {data: [[], [], []]}
                })
            context.result = {
                total: res.data.result_count + context.result.total,
                data: [...context.result.data, ...await Promise.all(res.data.results.map((a, i) => dataMap(a, i)))],
                limit: 10,
                skip: 0
            }
        }
    }
    return context;
}

const planProviders = async (context: HookContext): Promise<HookContext> => {
    const { plan_providers } = context.params.runJoin || {};
    if (plan_providers) {
        const ids:Array<any> = [];
        const crossSection = await new CoreCall('cross-sections', context).find({ query: { $limit: 1, hackId: `plan_providers:plans:${plan_providers}`}});
        if (crossSection.total) {
            for(const id of crossSection.data[0]?.sections?.practitioners || []) {ids.push(ObjectId.createFromHexString(id))}
        }
        context.params.query._id = { $in: ids }
    }
    return context;
}

const authenticate = async (context: HookContext) => {
    const writer = [['practitioners', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['practitioners', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']],
        or: '*'
    })(context) as any;
}

const dedup = async (context: HookContext) => {
    if(context.data.npi) {
        const ex = await new CoreCall('practitioners', context).find({ query: { $limit: 1, npi: context.data.npi }})
        if(ex.total){
            context.result = ex.data[0]
        }
    }
    return context;
}

const relateProviders = async (context: HookContext) => {
    const relateOne = async (path:string) => {
        context = await relate('otm', { herePath: `providers.${path}.id`, therePath: 'practitioners', thereService: 'providers', paramsName: `practitionerProvider_${path}`})(context)
        return context;
    }
    let providers = context.data.providers;
    if(context.type === 'before') {
        if (context.method !== 'create') {
            const existing = await loadExists(context);
            context = setExists(context, existing);
            providers = existing.providers
        }
    } else providers = context.result.providers
    if(Object.keys(providers || {}).length) await Promise.all(Object.keys(providers).map(a => relateOne(a)))
    return context;
}

const removeAutoCreate = (context: HookContext) => {
    if(context.data.person || context.data.$set?.person){
        context.data.$unset = {...context.data.$unset, auto_created: ''}
    }
    return context;
}


export const practitioners = (app: Application) => {
    // Register our service on the Feathers application
    app.use(practitionersPath, new PractitionersService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: practitionersMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(practitionersPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(practitionersExternalResolver),
                schemaHooks.resolveResult(practitionersResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(practitionersQueryValidator),
                schemaHooks.resolveQuery(practitionersQueryResolver)
            ],
            find: [
                hackIdBefore('plan_providers', 'plans', 'practitioners'),
                planProviders
            ],
            get: [],
            create: [
                schemaHooks.validateData(practitionersDataValidator),
                schemaHooks.resolveData(practitionersDataResolver),
                dedup,
                relateProviders
            ],
            patch: [
                schemaHooks.validateData(practitionersPatchValidator),
                schemaHooks.resolveData(practitionersPatchResolver),
                relateProviders,
                removeAutoCreate
            ],
            remove: [relateProviders]
        },
        after: {
            all: [],
            find: [
                searchApi,
                hackIdAfter('plan_providers', 'plans')
            ],
            create: [relateProviders],
            patch: [relateProviders],
            remove: [relateProviders]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations' {
    interface ServiceTypes {
        [practitionersPath]: PractitionersService
    }
}
