// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type { Cats, CatsData, CatsPatch, CatsQuery } from './cats.schema.js'

export type { Cats, CatsData, CatsPatch, CatsQuery }

export interface CatsParams extends MongoDBAdapterParams<CatsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class CatsService<ServiceParams extends Params = CatsParams> extends MongoDBService<
  Cats,
  CatsData,
  CatsParams,
  CatsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('cats')),
    operators: ['$regex', '$options']
  }
}
