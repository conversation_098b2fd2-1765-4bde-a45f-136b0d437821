// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import {authenticate} from '@feathersjs/authentication'
import {iff, isProvider, discard, preventChanges} from "feathers-hooks-common";
import {hooks as schemaHooks} from '@feathersjs/schema'

import {
  fingerprintsDataValidator,
  fingerprintsPatchValidator,
  fingerprintsQueryValidator,
  fingerprintsResolver,
  fingerprintsExternalResolver,
  fingerprintsDataResolver,
  fingerprintsPatchResolver,
  fingerprintsQueryResolver
} from './fingerprints.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {FingerprintsService, getOptions} from './fingerprints.class.js'
import {fingerprintsPath, fingerprintsMethods} from './fingerprints.shared.js'
import {noThrowAuth} from 'feathers-ucan';
import {logChange} from '../../../utils/change-log.js';
import axios from 'axios';

export * from './fingerprints.class.js'
export * from './fingerprints.schema.js'

const validateTurnstile = async (context:HookContext) => {
  if(context.params.crypto?.turnstile) {
    const { turnstile_sk } = context.app.get('turnstile')
    const data:any = {
      secret: turnstile_sk,
      response: context.params.crypto.turnstile
    }
    if(context.params.ip) data.remoteip = context.params.ip;
    const res = await axios.post('https://challenges.cloudflare.com/turnstile/v0/siteverify', JSON.stringify(data), { headers: { 'Content-Type': 'application/json' } })
        .catch(err => {
          throw new Error(`Error validating turnstile: ${err.message}`)
        })

    const outcome = res.data;
    if(outcome.success) {
      context.data.turnstile = outcome;
    } else {
      throw new Error(`Could not validate turnstile: ${(outcome['error-codes'] || ['Unknown Error']).join(', ')}`)
    }
  }
  return context;
}
// A configure function that registers the service and its hooks via `app.configure`
export const fingerprints = (app: Application) => {
  // Register our service on the Feathers application
  app.use(fingerprintsPath, new FingerprintsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: fingerprintsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(fingerprintsPath).hooks({
    around: {
      all: [
        schemaHooks.resolveExternal(fingerprintsExternalResolver),
        schemaHooks.resolveResult(fingerprintsResolver)
      ]
    },
    before: {
      all: [
          noThrowAuth,
        logChange(),
        schemaHooks.validateQuery(fingerprintsQueryValidator),
        schemaHooks.resolveQuery(fingerprintsQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(fingerprintsDataValidator),
        schemaHooks.resolveData(fingerprintsDataResolver)
      ],
      patch: [
        validateTurnstile,
        iff(isProvider('external'),
            discard('visitorId'),
            preventChanges(
                true,
                'visitorId',
            )
        ),
        schemaHooks.validateData(fingerprintsPatchValidator),
        schemaHooks.resolveData(fingerprintsPatchResolver)
      ],
      remove: [
        authenticate('jwt')

      ]
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [fingerprintsPath]: FingerprintsService
  }
}
