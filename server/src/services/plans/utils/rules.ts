export declare type ParticipantSettings = {
    status: 'one' | 'four',
    contribution: number,
    taxStatus: 's' | 'm' | 'mfs'//married filing separate
}

export const complianceSettings = {
    hsa: {
        pretax: 1,
        limits: {
            single: 4150,
            family: 8400
        }
    },
    dcp: {
        pretax: 1,
        limits: {
            1: 5000,
            4: 5000,
            taxStatus: {
                mfs: 2500
            }
        }
    },
    fsa: {
        pretax: 1,
        limits: {
            single: 3200,
            family: 3200
        }
    },
    pop: {
        pretax: 1,
    },
    ebp: { //excepted benefit
        pretax: 1
    },
    def: {
        pretax: 2
    },
    cash: {
        pretax: 0
    }
}

export const costLimits = {
    premium: {},
    moop: {
        single: 9200,
        family: 18400
    },
    deductible: {}
}

export const dependents = {
    disability: {
        incomeLimit: 4500
    }
}
