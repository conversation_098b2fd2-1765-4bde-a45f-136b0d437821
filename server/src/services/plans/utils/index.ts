import {AnyObj} from '../../../../utils/types.js';

export const getOpenEnrolls = (plan:any) => {
    if(!plan?.enrollments) return [];
    return Object.keys(plan.enrollments).filter(a => plan.enrollments[a].open_enroll).sort((a, b) => b.localeCompare(a))
}
export const getLatestOpen = (plan:any):string => {
    const sorted = getOpenEnrolls(plan);
    return sorted[0]
}
export const getLatestOpenObj = (plan:any):AnyObj => {
    const sorted = getOpenEnrolls(plan);
    return (plan.enrollments || {})[sorted[0]];
}
