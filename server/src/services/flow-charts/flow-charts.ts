// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  flowChartsDataValidator,
  flowChartsPatchValidator,
  flowChartsQueryValidator,
  flowChartsResolver,
  flowChartsExternalResolver,
  flowChartsDataResolver,
  flowChartsPatchResolver,
  flowChartsQueryResolver
} from './flow-charts.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import { FlowChartsService, getOptions } from './flow-charts.class.js'
import { flowChartsPath, flowChartsMethods } from './flow-charts.shared.js'
import {sanitize} from '../../utils/sanitize/index.js';

export * from './flow-charts.class.js'
export * from './flow-charts.schema.js'
import {logChange} from '../../../utils/change-log.js';
import {allUcanAuth, CapabilityParts, anyAuth} from 'feathers-ucan';
const creator = [['flow-charts', 'WRITE']] as Array<CapabilityParts>;
const deleter = [['flow-charts', '*']] as Array<CapabilityParts>;
const ucanArgs = {
  create: anyAuth,
  patch: creator,
  update: creator,
  remove: deleter
}

const addParent = (context) => {
  const addParent = (node) => {
    if(node.label) node.label = sanitize(node.label);
    if(node.text) node.label = sanitize(node.text);
    if(node.children) {
      Object.keys(node.children).map(a => {
        if(node.id) node.children[a].parent = node.id;
        node.children[a] = addParent(node.children[a]);
      });
    }
    return node;
  };
  context.data.nodes = context.data.nodes.map(a => addParent(a));
  return context;
};

// A configure function that registers the service and its hooks via `app.configure`
export const flowCharts = (app: Application) => {
  // Register our service on the Feathers application
  app.use(flowChartsPath, new FlowChartsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: flowChartsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(flowChartsPath).hooks({
    around: {
      all: [schemaHooks.resolveExternal(flowChartsExternalResolver), schemaHooks.resolveResult(flowChartsResolver)]
    },
    before: {
      all: [
        allUcanAuth<HookContext>(ucanArgs),
        logChange(),
        schemaHooks.validateQuery(flowChartsQueryValidator),
        schemaHooks.resolveQuery(flowChartsQueryResolver)
      ],
      find: [],
      get: [],
      create: [schemaHooks.validateData(flowChartsDataValidator), schemaHooks.resolveData(flowChartsDataResolver), addParent],
      patch: [schemaHooks.validateData(flowChartsPatchValidator), schemaHooks.resolveData(flowChartsPatchResolver), addParent],
      remove: []
    },
    after: {
      all: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [flowChartsPath]: FlowChartsService
  }
}
