// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import { hooks as schemaHooks } from '@feathersjs/schema'

import {
  changeLogsDataValidator,
  changeLogsPatchValidator,
  changeLogsQueryValidator,
  changeLogsResolver,
  changeLogsExternalResolver,
  changeLogsDataResolver,
  changeLogsPatchResolver,
  changeLogsQueryResolver
} from './change-logs.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import { ChangeLogsService, getOptions } from './change-logs.class.js'
import { changeLogsPath, changeLogsMethods } from './change-logs.shared.js'

import {logChange} from '../../../utils/change-log.js';
import {relate} from '../../../utils/relate/index.js';
import {noThrowAuth} from 'feathers-ucan';

export * from './change-logs.class.js'
export * from './change-logs.schema.js'

const relateRecord = async (context:HookContext):Promise<HookContext> => {
  if(context.params.skip_hooks) return context;
  return await relate('oto', {
    herePath: 'recordId',
    therePath: 'changeLog',
    thereService: context.data.service,
    paramsName: 'recordChangelog'
  })(context);
}

// A configure function that registers the service and its hooks via `app.configure`
export const changeLogs = (app: Application) => {
  // Register our service on the Feathers application
  app.use(changeLogsPath, new ChangeLogsService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: changeLogsMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(changeLogsPath).hooks({
    around: {
      all: [
        schemaHooks.resolveExternal(changeLogsExternalResolver),
        schemaHooks.resolveResult(changeLogsResolver)
      ]
    },
    before: {
      all: [
          noThrowAuth,
        logChange(),
        schemaHooks.validateQuery(changeLogsQueryValidator),
        schemaHooks.resolveQuery(changeLogsQueryResolver)
      ],
      find: [],
      get: [],
      create: [
        schemaHooks.validateData(changeLogsDataValidator),
        schemaHooks.resolveData(changeLogsDataResolver),
        relateRecord
      ],
      patch: [
        schemaHooks.validateData(changeLogsPatchValidator),
        schemaHooks.resolveData(changeLogsPatchResolver)
      ],
      remove: [relateRecord]
    },
    after: {
      all: [],
      create: [
          relateRecord
      ],
      remove: [relateRecord]
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [changeLogsPath]: ChangeLogsService
  }
}
