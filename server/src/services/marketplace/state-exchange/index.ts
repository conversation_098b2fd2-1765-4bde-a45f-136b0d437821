import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';
import {find_ptc_with_income_and_slcsp, fpl_levels} from '../utils/aca.js';

/** percent of federal poverty level corresponding with the csr plan_id extension on silver plans */
const csrIncomes = {
    150: '04',
    200: '03',
    250: '02'
}

type facet_groups = Array<{
    name: string, //issuers, types, metalLevels
    facets: Array<string>
}>

type HhPremiumOpts = {
    enrollment?: any,
    enrolled?: Array<{ age: number | string, relation: string } & { [key: string]: any }>,
    place?: { countyfips: string, state: string, zipcode: string }
}
export const getHouseholdPremium = (plcy: any, {enrollment, enrolled, place}: any) => {
    if (!plcy?.rating_areas) return undefined;
    const e = enrollment || {}
    let areaData;

    const fips = place?.countyfips || e.county?.fips;
    if(fips){
        for(const k in plcy.rating_areas){
            if((plcy.rating_areas[k]?.fips || []).includes(fips)) {
                areaData = plcy.rating_areas[k]
                break;
            }
        }
    }
    if(!areaData){
        const zip = place?.zipcode || e.address?.postal;
        if (!zip) return undefined
        for (const k in plcy.rating_areas) {
            if ((plcy.rating_areas[k]?.zips || []).includes(zip)) {
                areaData = plcy.rating_areas[k];
                break;
            }
        }
    }

    /** MUST HAVE A MATCHING FIPS or ZIP TO RETURN A RATE */
    if (!areaData) return undefined
    enrolled = getEnrolled({enrollment, enrolled})
    if (enrolled.length && enrolled.filter(a => !a.relation).length) enrolled = simRelations(enrolled);
    let deps: any = [];
    let self;
    let spouse;
    if (!enrolled.length) return undefined;
    for (const e of enrolled) {
        if (e.relation === 'self') self = e;
        else if (e.relation === 'spouse') spouse = e;
        else deps.push(e)
    }
    deps = deps.sort((a, b) => b - a).slice(0, 3)
    let rate = 0;
    if (self) rate += areaData.rates[self.age] || 0;
    if (spouse) rate += areaData.rates[spouse.age] || 0;
    for (const dep of deps.slice(0,3)) {
        rate += areaData.rates[dep.age]
    }
    return rate
}

const personToEnrolled = (p: any, relation?: string) => {
    return {
        relation: p.relation || (p.child ? 'child' : Number(p.age) < 18 ? 'child' : relation),
        age: p.age || p.age === 0 ? p.age : getAge(p.dob)
    }
}

const getPlanVersion = (p: any, household: any) => {
    const length = household.people?.length;
    if (!length) return p;
    const income = household.income || household.income === 0 ? household.income : 10000000;
    if (!income) return p;
    const fpl = fpl_levels[length];
    const percent_of_fpl = (income / fpl) * 100;
    let version = '01';
    for (const k in csrIncomes) {
        if (percent_of_fpl <= Number(k)) {
            version = csrIncomes[k];
            break;
        }
    }
    if (version === '01') return p;
    if (!p.csr) return p;
    const csr = p.csr;
    if (!csr[version]) return p;
    return {...p, ...csr[version]}

}

const formatPlan = ({plan: p, household, place, aptc}: any) => {
    const plan = getPlanVersion(p, household)
    plan.premium = getHouseholdPremium(plan, {enrolled: household.people.map(a => personToEnrolled(a)), place})
    plan.aptc_eligible_premium = Math.min(aptc, plan.premium)
    return plan
}

export const stateExchangeSlcsp = ({place, business_year, household, aptc}: any) => {
    return async (context: HookContext) => {
        const silvers = await new CoreCall('se-plans', context).find({
            query: {
                business_year: business_year || Number(new Date().getFullYear()),
                all_fips: {$in: [place.countyfips]},
                metal: 'silver',
                $sort: {fortyPremium: 1},
                $limit: 10,
                $skip: 0
            }
        })
            .catch(err => {
                console.log(`Error getting slcsp: ${err.message}`)
                return {data: []}
            })
        return silvers.data.map(a => formatPlan({
            plan: a,
            household,
            place,
            aptc
        })).sort((a, b) => a.premium - b.premium)[1]
    }
}

export const stateExchangeGet = async (context: HookContext) => {
    const {business_year, id, place, filter, household, $skip, $limit} = context.params.query
    const ids = Array.isArray(id) ? id : [id]
    const query: any = {
        state_code: place.state,
        business_year: business_year || Number(new Date().getFullYear()),
        $limit: filter?.limit || $limit || 50,
        $sort: {fortyPremium: 1},
        $skip,
        all_fips: {$in: [place.countyfips]}
    }
    const slcsp = await stateExchangeSlcsp({place, business_year, household})(context)

    const aptc = slcsp ? find_ptc_with_income_and_slcsp({
        income: household.income,
        members: household.people.length,
        slcsp: slcsp?.premium * 12
    }).ptc : 0

    delete query.all_fips;
    const oids:any = []
    const plan_ids:any = [];
    for(const id of ids) {
        if (!id) continue;
        if (id.length === 24) oids.push(id)
        else plan_ids.push(id)
    }
    if(!oids.length && !plan_ids.length) throw new Error('No valid id passed to state exchange get')
    if(oids.length) query._id = {$in: oids}
    if(plan_ids.length) {
        if(oids.length) {
            delete query._id;
            query.$or = [
                {_id: {$in: oids}},
                {plan_id: {$in: plan_ids}}
            ]
        } else query.plan_id = {$in: plan_ids}
    }

    const plans = await new CoreCall('se-plans', context).find({
        query: {
            ...query,
            $limit: oids.length + plan_ids.length
        }
    })
    return plans.data.map(a => formatPlan({plan: a, household, place, aptc}))
}

export const stateExchangeSearch = async (context: HookContext) => {
    const {business_year, place, filter, household, $skip, $limit} = context.params.query

    const query: any = {
        state_code: place.state,
        fortyPremium: { $exists: true },
        business_year: business_year || Number(new Date().getFullYear()),
        $limit: filter?.limit || $limit || 50,
        $sort: {fortyPremium: 1},
        $skip,
        all_fips: {$in: [place.countyfips]}
    }
    if (filter?.metal_level) query.metal = {$in: filter.metal_level}
    const se = await new CoreCall('se-plans', context).find({
        query
    })

    const metalFn = () => {
        return {data: [], facet_groups: [], total: 0}
    }
    const metals: any = {
        gold: metalFn(),
        silver: metalFn(),
        bronze: metalFn(),
        platinum: metalFn()
    }

    const facetFn = () => {
        return {
            types: [],
            issuers: [],
            metalLevels: []
        }
    }
    const facets: any = {
        gold: facetFn(),
        silver: facetFn(),
        bronze: facetFn(),
        platinum: facetFn(),
    }

    const allFacets: any = facetFn()

    for (const plan of se.data) {
        const metal = plan.metal?.toLowerCase();
        if (metals[metal]) {
            metals[metal].data.push(formatPlan({plan, household, place}));
            metals[metal].total++
            if (plan.type && !facets[metal].types.includes(plan.type)) {
                facets[metal].types.push(plan.type)
                if (!allFacets.types.includes(plan.type)) allFacets.types.push(plan.type)
            }
            if (plan.carrierName && !facets[metal].issuers.includes(plan.carrierName)) {
                facets[metal].issuers.push(plan.carrierName)
                if (!allFacets.issuers.includes(plan.carrierName)) allFacets.issuers.push(plan.carrierName)
            }
            if (!facets[metal].metalLevels.includes(plan.metal)) {
                facets[metal].metalLevels.push(plan.metal)
                if (!allFacets.metalLevels.includes(plan.metal)) allFacets.metalLevels.push(plan.metal)

            }
        }
    }

    for (const k in metals) {
        metals[k].data = metals[k].data.sort((a, b) => a.premium - b.premium)
    }

    let slcsp
    if ($skip === 0) {
        slcsp = metals.silver.data[1]
    } else {
        slcsp = await stateExchangeSlcsp({place, business_year, household})(context)
    }

    const aptc = find_ptc_with_income_and_slcsp({
        income: household.income,
        members: household.people.length,
        slcsp: slcsp?.premium * 12
    }).ptc

    const facetMap = (a:any) => {
        return {
            value: a,
            count: a.length
        }
    }
    for (const k in facets) {
        const types = {name: 'types', facets: facets[k].types.map(a => facetMap(a))}
        const issuers = {name: 'issuers', facets: facets[k].issuers.map(a => facetMap(a))}
        const metalLevels = {
            name: 'metalLevels', facets: facets[k].metalLevels.map(a => facetMap(a))
        }
        metals[k].facet_groups = [types, issuers, metalLevels]
    }

    const facet_groups = [{name: 'types', facets: allFacets.types.map(a => facetMap(a))}, {
        name: 'issuers',
        facets: allFacets.issuers.map(a => facetMap(a))
    }, {name: 'metalLevels', facets: allFacets.metalLevels.map(a => facetMap(a))}]

    for (const k in metals) {
        metals[k].data = metals[k].data.map(a => {
            a.aptc_eligible_premium = Math.min(aptc, a.premium);
            return a
        })
    }
    context.result = {
        data: [],
        total: se.total,
        limit: query.$limit,
        skip: query.$skip,
        facet_groups,
        gold: metals.gold,
        silver: metals.silver,
        bronze: metals.bronze,
        platinum: metals.platinum,
        slcsp,
        aptc
    }
    return context;
}
