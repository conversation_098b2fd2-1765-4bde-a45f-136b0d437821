
export * from './hooks.js'


export const benefit_keys = {
    "adult_dental": true,
    "age29_rider": false,
    "dp_rider": false,
    "fp_rider": false,
    "hsa_eligible": false,
    "imaging": "In-Network: 50% / Out-of-Network: Not Covered",
    "out_of_network_coverage": false,
    "preferred_brand_drugs": "In-Network: $195 / Out-of-Network: Not Covered",
    "prenatal_postnatal_care": "In-Network: $60 / Out-of-Network: Not Covered",
    "ambulance": "In-Network: 50% / Out-of-Network: 50%",
    "child_eyewear": "In-Network: $0 / Out-of-Network: Not Covered | limit: 1 item(s) per year",
    "child_eye_exam": "In-Network: $0 / Out-of-Network: Not Covered | limit: 1 exam(s) per year",
    "durable_medical_equipment": "In-Network: 50% / Out-of-Network: Not Covered",
    "diagnostic_test": "In-Network: 50% / Out-of-Network: Not Covered",
    "emergency_room": "In-Network: $2,500 / Out-of-Network: $2,500",
    "generic_drugs": "In-Network: $3 / Out-of-Network: Not Covered",
    "habilitation_services": "In-Network: 50% / Out-of-Network: Not Covered | limit: 30 visit(s) per year",
    "home_health_care": "In-Network: 50% / Out-of-Network: Not Covered",
    "hospice_service": "In-Network: 50% / Out-of-Network: Not Covered",
    "inpatient_birth": "In-Network: $3,000 / Out-of-Network: Not Covered",
    "inpatient_facility": "In-Network: $3,000 per day / Out-of-Network: Not Covered",
    "inpatient_mental_health": "In-Network: $3,000 per day / Out-of-Network: Not Covered",
    "inpatient_physician": "In-Network: $0 / Out-of-Network: Not Covered",
    "inpatient_substance": "In-Network: $3,000 per day / Out-of-Network: Not Covered",
    "non_preferred_brand_drugs": "In-Network: 45% after deductible / Out-of-Network: Not Covered",
    "outpatient_facility": "In-Network: 50% / Out-of-Network: Not Covered",
    "outpatient_mental_health": "In-Network: $60 / Out-of-Network: Not Covered",
    "outpatient_physician": "In-Network: 50% / Out-of-Network: Not Covered",
    "outpatient_substance": "In-Network: $60 / Out-of-Network: Not Covered",
    "preventive_care": "In-Network: $0 / Out-of-Network: Not Covered",
    "primary_care_physician": "In-Network: $60 / Out-of-Network: Not Covered",
    "rehabilitation_services": "In-Network: 50% / Out-of-Network: Not Covered | limit: 30 visit(s) per year",
    "skilled_nursing": "In-Network: $3,000 per day / Out-of-Network: Not Covered | limit: 60 day(s) per year",
    "specialist": "In-Network: $115 / Out-of-Network: Not Covered",
    "specialty_drugs": "In-Network: 50% after deductible / Out-of-Network: Not Covered",
    "urgent_care": "In-Network: $60 / Out-of-Network: Not Covered",
    "chiropractic_services": true,
    "gated": false,
    "mail_order_rx": null,
    "child_dental": "In-Network: Not Covered / Out-of-Network: Not Covered",
    "imaging_center": "In-Network: 50% / Out-of-Network: Not Covered",
    "imaging_physician": "In-Network: 50% / Out-of-Network: Not Covered",
    "lab_test": "In-Network: $60 / Out-of-Network: Not Covered",
    "nonpreferred_generic_drug_share": "In-Network: Not Applicable / Out-of-Network: Not Applicable",
    "nonpreferred_specialty_drug_share": "In-Network: Not Applicable / Out-of-Network: Not Applicable",
    "outpatient_ambulatory_care_center": "In-Network: 50% / Out-of-Network: Not Covered",
    "prenatal_care": "In-Network: $60 / Out-of-Network: Not Covered",
    "postnatal_care": "In-Network: $60 / Out-of-Network: Not Covered",
    "abortion_rider": false,
    "plan_coinsurance": "In-Network: 50% / Out-of-Network: Not Applicable",
    "inpatient_birth_physician": "In-Network: $0 / Out-of-Network: Not Covered",
    "infertility_treatment_rider": true,
    "telemedicine": null,
}

const genCategories = (val:number) => {
    const obj:any = {};
    for(let i = 0; i < coins_categories.length; i++) {
        obj[coins_categories[i]] = val;
    }
    return obj
}

const networkDef = () => {
    return {
        categories: {},
        display: [],
        avg: 0
    }
}
const defBen = () => {
    return {
        in_network: networkDef(),
        in_network2: networkDef(),
        combined: networkDef(),
        oon: networkDef()
    }
}
const parseBenefits = (policy: Policy) => {
    const coins: any = defBen()
    const copay: any = defBen()
    const benefits: any = {};

    if(policy.plan_coinsurance) {
        const getNum = (s:string) => {
            const num = Number(s.split(' ').filter(a => a.includes('$'))[0]?.replace('%', '').trim() || 100) || 100
            if(isNaN(num)) return 100;
            return num;
        }
        const spl = policy.plan_coinsurance.split('/')
        for (let i = 0; i < spl.length; i++) {
            const v = spl[i].toLowerCase()
            const num = getNum(v) / 100
            const obj = {
                avg: num,
                display: [v],
                categories: genCategories(num)
            }
            if (v.includes('in-network')) coins.in_network = obj
            else if(v.includes('out-of')) coins.oon = obj
        }
    } else {
        let co = policy.coinsurance;
        if(!co && co !== 0) co = 1;
        coins.in_network = { avg: co, categories: genCategories(co)}
    }

    for(const k in benefit_keys){
        const v = policy[k];
        if(v){
            benefits[k] = { detail: benefits[k], label: k.split('_').join(' '), covered: v.includes('$') || v.includes('%')}
        }
    }

    return {coins, copay, benefits}
}

const getPolicyNames = (policy: any) => {
    const nm = (policy?.name || '')
    const plus = nm.split('+');
    const line = nm.split('|');
    return {
        title: plus[0].split('|')[0],
        subtitle: plus[1] ? plus.slice(1).join('+') : line ? line.slice(1).join('|') : ''
    }
}
const metals = ['platinum', 'gold', 'silver', 'bronze'];
const getMetal = (val: string) => {
    if (!val) return '';
    const lower = val.toLowerCase();
    let metal = lower;
    for (let i = 0; i < metals.length; i++) {
        if (lower.includes(metals[i])) {
            metal = metals[i];
            break;
        }
    }
    return metal;
}

const getMoopOrDed = (policy: Policy, path: 'moop' | 'deductible') => {
    const parseOop = (str: string) => {
        if (!str) return undefined;
        const getNum = (s: string) => {
            return Number(s.split(' ').filter(a => a.includes('$'))[0]?.replace('$', '').replace(',', '').trim())
        }
        const obj: any = {};
        const spl = str.split('/')
        for (let i = 0; i < spl.length; i++) {
            const v = spl[i].toLowerCase()
            if (v.includes('in-network')) obj.in_network = getNum(v)
            else if (v.includes('out-of')) obj.oon = getNum(v)
        }
        return obj;
    }
    return {
        medical: {
            single: parseOop(policy[`individual_medical_${path}`]),
            family: parseOop(policy[`family_medical_${path}`])
        },
        drug: {
            single: parseOop(policy[`individual_drug_${path}`]),
            family: parseOop(policy[`family_drug_${path}`])
        }
    }
}

export const normalizeIdeonPolicy = (policy: Policy): NormPolicy => {
    const {coins, copay, benefits} = parseBenefits(policy);
    const {title, subtitle} = getPolicyNames(policy);
    return {
        _id: policy.id,
        plan_id: policy.id,
        name: policy.name,
        acaPlan: true,
        type: 'aca',
        title,
        subtitle,
        metal: getMetal(policy.level),
        carrierName: policy.carrier?.name || '',
        carrierLogo: policy.logo_url || policy.carrier?.logo_url,
        plan_type: policy.plan_type,
        premium: policy.premium,
        aptc_eligible_premium: undefined,
        on_exchange: policy?.on_market,
        off_exchange: !policy?.on_market,
        benefits,
        copay,
        coins,
        moop: getMoopOrDed(policy, 'moop'),
        deductible: getMoopOrDed(policy, 'deductible'),
        hsa_eligible: policy.hsa_eligible,
        eligible_dependents: policy.issuer?.eligible_dependents,
        benefits_url: policy.benefits_summary_url,
        brochure_url: (policy.plandocuments || [])[0]?.url,
        formulary_url: policy.drug_formulary_url,
        network_url: (policy.networks || [])[0]?.provider_directory_url,
        tobacco_lookback: undefined,
        exclusions: ''
    }
}
