import {AnyObj} from '../../../utils/types.js';
import {Type, ObjectIdSchema} from '@feathersjs/typebox';

export const costLimits: any = {
    premium: {},
    moop: {
        single: 9200,
        family: 18400
    },
    deductible: {}
}

export const coins_categories = ['emergency_room', 'primary_care', 'urgent_care', 'dental', 'specialist', 'mental', 'drug'];

export const category_weights = {
    emergency_room: .25,
    primary_care: .1,
    urgent_care: .2,
    specialist: .2,
    dental: .05,
    drug: .2
}





export type CoCategories = {
    emergency_room: number,
    primary_care: number,
    urgent_care: number,
    specialist: number,
    dental: number,
    drug: number
}
type BenNetwork = {
    avg: number,
    display: Array<string>,
    categories: CoCategories
}
type Benefit = {
    in_network: BenNetwork,
    in_network2: BenNetwork,
    combined: BenNetwork,
    oon: BenNetwork
}
type OopNetwork = Partial<{
    in_network: number,
    in_network2: number,
    combined: number,
    oop: number
}>
type Oop = {
    medical: {
        single: OopNetwork,
        family: OopNetwork
    },
    drug: {
        single: OopNetwork,
        family: OopNetwork
    }
}
type BenefitDisplay = { label: string, covered: boolean, detail?: string }
export type NormPolicy = {
    _id: string,
    plan_id: string,
    acaPlan: boolean,
    type: string,
    name: string,
    title: string,
    state_code?: string,
    subtitle: string,
    business_year?: number,
    premium?: number,
    aptc_eligible_premium?: number,
    eligible_dependents: Array<string>,
    hsa_eligible: boolean,
    issuer_id?: string,
    carrierName: string,
    carrierLogo?: string,
    plan_type: string, //HMO, PPO
    benefits_url: string,
    formulary_url: string,
    network_url: string,
    brochure_url: string,
    benefits: { [key: string]: BenefitDisplay },
    exclusions: string,
    on_exchange: boolean,
    off_exchange: boolean,
    tobacco_lookback?: number,
    tobacco?: string,
    moop: Partial<Oop>,
    deductible: Partial<Oop>,
    metal: string,
    coins: Benefit
    copay: Benefit
}

export type Policy = AnyObj

const oopLimit = Type.Object(
    {
        in_network: Type.Optional(Type.Number()),
        in_network2: Type.Optional(Type.Number()),
        combined: Type.Optional(Type.Number()),
        oop: Type.Optional(Type.Number())
    },
    { additionalProperties: true }
);

const oopGroup = Type.Object(
    {
        single: Type.Optional(oopLimit),
        family: Type.Optional(oopLimit)
    },
    { additionalProperties: true }
);

const oopSchema = Type.Object(
    {
        medical: Type.Optional(oopGroup),
        drug: Type.Optional(oopGroup)
    },
    {
        description:
            'For each key, medical and drug provide single and family in_network, tier2 network, combined, and out of network limits',
        additionalProperties: true
    }
);

const coinsCategories = Type.Object(
    {
        emergency_room: Type.Optional(Type.Number()),
        primary_care:   Type.Optional(Type.Number()),
        urgent_care:    Type.Optional(Type.Number()),
        specialist:     Type.Optional(Type.Number()),
        dental:         Type.Optional(Type.Number()),
        drug:           Type.Optional(Type.Number()),
    },
    { additionalProperties: true }
);

const coinsBlock = Type.Object(
    {
        avg: Type.Optional(Type.Number()),
        display: Type.Optional(Type.Array(Type.String())),
        categories: Type.Optional(coinsCategories),
    },
    { additionalProperties: true }
);

const acaCoinsSchema = Type.Object(
    {
        in_network:  Type.Optional(coinsBlock),
        in_network2: Type.Optional(coinsBlock),
        combined:    Type.Optional(coinsBlock),
        oon:         Type.Optional(coinsBlock),
    },
    {
        description:
            'for each key - in_network, in_network2, combined, oon - provide coinsurance or copay rates for 5 care types, an average for representing it in a single number, and a display array for how to describe or display to users',
        additionalProperties: true,
    }
);

export const benSchema = Type.Record(
    Type.String({ pattern: '^.*$' }),
    Type.Object({
        label: Type.Optional(Type.String()),
        covered: Type.Optional(Type.Boolean()),
        detail: Type.Optional(Type.String()),
        cat: Type.Optional(ObjectIdSchema())
    }),
    {
      description: 'any key with benSchema as the details of the benefit'
    }
);

export const normPolicySchema = Type.Object(
    {
        acaPlan: Type.Optional(Type.Boolean()),
        plan_id: Type.Optional(Type.String()),
        state_code: Type.Optional(Type.String()),
        business_year: Type.Optional(Type.Number()),
        import_date: Type.Optional(Type.Any()),
        rate_expiration: Type.Optional(Type.Any()),
        rate_effective_date: Type.Optional(Type.Any()),
        state_plan_id: Type.Optional(Type.String()),
        issuer_id: Type.Optional(Type.String()),
        type: Type.Optional(Type.String()),
        name: Type.Optional(Type.String()),
        title: Type.Optional(Type.String()),
        subtitle: Type.Optional(Type.String()),
        premium: Type.Optional(Type.Number()),
        aptc_eligible_premium: Type.Optional(Type.Number()),
        eligible_dependents: Type.Optional(Type.Array(Type.String())),
        hsa_eligible: Type.Optional(Type.Boolean()),
        carrierName: Type.Optional(Type.String()),
        carrierLogo: Type.Optional(Type.String()),
        plan_type: Type.Optional(Type.String()),
        benefits_url: Type.Optional(Type.String()),
        formulary_url: Type.Optional(Type.String()),
        network_url: Type.Optional(Type.String()),
        brochure_url: Type.Optional(Type.String()),
        benefits: Type.Optional(benSchema),
        exclusions: Type.Optional(Type.String()),
        on_exchange: Type.Optional(Type.Boolean()),
        off_exchange: Type.Optional(Type.Boolean()),
        tobacco_lookback: Type.Optional(Type.Number()),
        tobacco: Type.Optional(Type.String()),
        moop: Type.Optional(oopSchema),
        deductible: Type.Optional(oopSchema),
        metal: Type.Optional(Type.String()),
        coins: Type.Optional(acaCoinsSchema),
        copay: Type.Optional(acaCoinsSchema),
    },
    { additionalProperties: true }
);
