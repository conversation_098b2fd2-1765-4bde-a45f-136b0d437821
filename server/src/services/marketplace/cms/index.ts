
export * from './hooks.js'
import { costLimits, coins_categories, category_weights, CoCategories, Policy, NormPolicy } from '../utils/index.js';

export const nonMarketplaceStates = [
    "CA", // California
    "CO", // Colorado
    "CT", // Connecticut
    "DC", // District of Columbia
    "GA", // Georgia
    "ID", // Idaho
    "KY", // Kentucky
    "ME", // Maine
    "MD", // Maryland
    "MA", // Massachusetts
    "MN", // Minnesota
    "NV", // Nevada
    "NJ", // New Jersey
    "NM", // New Mexico
    "NY",
    "PA",
    "RI",
    "VT",
    "VA",
    "WA"
]

const stateDefs = {
    'OR': { state: 'OR', zipcode: '97267', countyfips: '41005'},
    'NC': { state: 'NC', zipcode: '27403', countyfips: '37081'},
    'UT': { state: 'UT', zipcode: '84067', countyfips: '49057'},
    'KS': { state: 'KS', zipcode: '66160', countyfips: '20209' },
    'IL': { state: 'IL', zipcode: '62702', countyfips: '17167' },
    'NE': { state: 'NE', zipcode: '68502', countyfips: '31109' },
    'DE': { state: 'DE', zipcode: '19940', countyfips: '10005' },
}
export const marketplaceDefMap = {
    "CA": stateDefs.OR,
    "CO": stateDefs.UT,
    "CT": stateDefs.DE,
    "DC": stateDefs.NC,
    "GA": stateDefs.NC,
    "ID": stateDefs.UT,
    "KY": stateDefs.KS,
    "ME": stateDefs.DE,
    "MD": stateDefs.DE,
    "MA": stateDefs.DE,
    "MN": stateDefs.IL,
    "NV": stateDefs.UT,
    "NJ": stateDefs.DE,
    "NM": stateDefs.NE,
    "NY": stateDefs.DE,
    "PA": stateDefs.IL,
    "RI": stateDefs.DE,
    "VT": stateDefs.DE,
    "VA": stateDefs.NC,
    "WA": stateDefs.OR
}

const parseCategory = (str: string) => {
    if (!str) return undefined;
    let cat;
    for (let i = 0; i < coins_categories.length; i++) {
        if (str.toLowerCase().includes(coins_categories[i])) {
            cat = coins_categories[i];
            break;
        }
    }
    return cat;
}
export const blendCoins = (obj: CoCategories) => {
    let total = 0;
    for (const k in obj) {
        total += obj[k] * (category_weights[k] || .2)
    }
    return total
}

export const getNetworkKey = (str: string) => {
    if(!str) return 'combined'
    const lower = str.toLowerCase();
    if (lower === 'in-network') return 'in_network'
    if (lower.includes('tier 2')) return 'in_network2';
    if (lower.includes('out-of')) return 'oon';
    return 'combined'
}

export const networkDef = () => {
    return {
        categories: {},
        display: [],
        avg: 0
    }
}
export const defBen = () => {
    return {
        in_network: networkDef(),
        in_network2: networkDef(),
        combined: networkDef(),
        oon: networkDef()
    }
}

const parseBenefits = (policy: Policy) => {
    const coins: any = defBen()
    const copay: any = defBen()
    const benefits:any = {};
    const bens: any = (policy?.benefits || []);
    for (let i = 0; i < bens.length; i++) {
        const benPath = bens[i].type || bens[i].name
        benefits[benPath] = { label: bens[i].name, covered: bens[i].covered, detail: `${bens[i].explanation ? bens[i].explanation + ' | ' : ''}${bens[i].exclusions ? bens[i].exclusions + ' | ' : ''}` };
        const costs = bens[i].cost_sharings || []
        const category = parseCategory((bens[i].type || bens[i].name)?.split(' ').join('_'));
        if (category) {
            for (let cs = 0; cs < costs.length; cs++) {
                const cost_share = costs[cs];
                benefits[benPath].detail += `${cost_share.network_tier}: ${cost_share.display_string} | `
                const networkKey = getNetworkKey(cost_share.network_tier)
                if (networkKey) {
                    const coins_rate = cost_share.coinsurance_rate
                    const copay_rate = cost_share.copay_amount;
                    if (coins_rate || coins_rate === 0) {
                        coins[networkKey].display.push(`${bens[i].name}|coinsurance|${cost_share.display_string}`)
                        coins[networkKey].categories[category] = coins_rate;
                    }
                    if (copay_rate || copay_rate === 0) {
                        copay[networkKey].display.push(`${bens[i].name}|copay|${cost_share.display_string}`)
                        copay[networkKey].categories[category] = copay_rate;
                    }
                }
            }
        }
    }
    for(const k in coins){
        coins[k].avg = blendCoins(coins[k].categories)
        copay[k].avg = blendCoins(copay[k].categories)
    }
    return { coins, copay, benefits }
}

export const getPolicyNames = (policy:any) => {
    const nm = (policy?.name || '')
    const plus = nm.split('+');
    const line = nm.split('|');
    return {
        title: (plus[0].split('|')[0]).trim(),
        subtitle: (plus[1] ? plus.slice(1).join('+') : line ? line.slice(1).join('|') : '').trim()
    }
}
export const metals = ['platinum', 'gold', 'silver', 'bronze'];
export const getMetal = (val:string) => {
    if(!val) return '';
    const lower = val.toLowerCase();
    let metal = lower;
    for(let i = 0; i < metals.length; i++){
        if(lower.includes(metals[i])){
            metal = metals[i];
            break;
        }
    }
    return metal;
}

type MoopOptions = {
    type?:'family'|'single'
}
const getMoopOrDed = (policy:Policy, path:'moops'|'deductibles') => {
    const sMax = costLimits.moop.single;
    const fMax = costLimits.moop.family
    let res:any = {
        medical: {
            single: {
                in_network: sMax,
            },
            family: {
                in_network: fMax,
            }
        },
        drug: {
            single: {
                in_network: sMax,
            },
            family: {
                in_network: fMax,
            }
        }
    }
    const loop = (arr:any[]) => {
        for(const oop of arr){
            const oop_type = oop.type?.toLowerCase()
            if(oop_type.includes('medical')){
                const key = getNetworkKey(oop.network_tier)
                res.medical.single[key] = Math.min(res.medical.single[key], oop.amount)
                if(oop.family) res.medical.family[key] = Math.min(oop.amount, res.medical.family[key])
            }
            if(oop_type.includes('drug')){
                const key = getNetworkKey(oop.network_tier)
                if(oop.individual) res.drug.single[key] = Math.min(res.drug.single[key] || costLimits.moop.single, oop.amount)
                if(oop.family) res.drug.family[key] = Math.min(res.drug.family[key] || costLimits.moop.family, oop.amount)
            }
        }
    }
    loop(policy[path] || [])
    loop(policy[`tiered_${path}`] || [])

    res.medical = {
        ...res.medical,
        single: {
            in_network: Math.min(res.medical.single.in_network || sMax, res.medical.family.in_network || sMax),
            in_network2: Math.min(res.medical.single.in_network2 || sMax, res.medical.family.in_network2 || sMax),
            oon: Math.min(res.medical.single.oon || Infinity, res.medical.family.oon || Infinity)
        }
    }
    res.drug = {
        ...res.drug,
        single: {
            in_network: Math.min(res.drug.single.in_network || sMax, res.drug.family.in_network),
            in_network2: Math.min(res.drug.single.in_network2 || sMax, res.drug.family.in_network2 || sMax),
            oon: Math.min(res.drug.single.oon || Infinity, res.drug.family.oon || Infinity)
        }
    }
    return res;
}

export const normalizeCmsPolicy = (policy:Policy):NormPolicy => {
    const { coins, copay, benefits } = parseBenefits(policy);
    const { title, subtitle } = getPolicyNames(policy);
    return {
        _id: policy._id || policy.id,
        plan_id: policy._id || policy.id,
        acaPlan: true,
        type: 'aca',
        name: policy.name,
        title,
        subtitle,
        metal: getMetal(policy.metal_level),
        carrierName: policy.issuer?.name || '',
        carrierLogo: '',
        plan_type: policy.type,
        premium: policy.premium,
        aptc_eligible_premium: policy.aptc_eligible_premium,
        on_exchange: true,
        off_exchange: false,
        benefits,
        copay,
        coins,
        moop: getMoopOrDed(policy, 'moops'),
        deductible: getMoopOrDed(policy, 'deductibles'),
        hsa_eligible: policy.hsa_eligible,
        eligible_dependents: policy.issuer?.eligible_dependents,
        benefits_url: policy.benefits_url,
        brochure_url: policy.brochure_url,
        formulary_url: policy.formulary_url,
        network_url: policy.network_url,
        tobacco_lookback: policy.tobacco_lookback,
        exclusions: policy.exclusions
    }
}
