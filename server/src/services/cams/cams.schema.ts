// TypeBox schema for cams service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, queryWrapper, commonPatch} from '../../utils/common/typebox-schemas.js'
import {compsCamsSchema} from '../comps/schemas/comps-cams.js';

export const camsSchema = Type.Object({
    _id: ObjectIdSchema(),
    person: ObjectIdSchema(),
    comp: Type.Optional(ObjectIdSchema()),
    hireDate: Type.Optional(Type.Any()),
    hoursWorked: Type.Optional(Type.Number()),
    off: Type.Optional(Type.Boolean()),
    stage: Type.Optional(Type.String()),
    active: Type.Optional(Type.String()),
    group: Type.Optional(ObjectIdSchema()),
    terminated: Type.Optional(Type.Boolean()),
    terminatedAt: Type.Optional(Type.Any()),
    terminatedBy: Type.Optional(ObjectIdSchema()),
    ...Type.Partial(Type.Object(compsCamsSchema)).properties,
    org: ObjectIdSchema(),
    terms: Type.Optional(Type.String()),
    limit: Type.Optional(Type.Number()),
    ...commonFields.properties
}, {additionalProperties: false})

export type Cams = Static<typeof camsSchema>
export const camsValidator = getValidator(camsSchema, dataValidator)
export const camsResolver = resolve<Cams, HookContext>({})
export const camsExternalResolver = resolve<Cams, HookContext>({})

export const camsDataSchema = Type.Object({
    ...Type.Omit(camsSchema, ['_id']).properties
}, {additionalProperties: false})

export type CamsData = Static<typeof camsDataSchema>
export const camsDataValidator = getValidator(camsDataSchema, dataValidator)
export const camsDataResolver = resolve<CamsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const camsQueryProperties = Type.Pick(camsSchema, ['_id', 'person', 'comp', 'group', 'terminatedBy', 'org', 'contract'])

export const camsPatchSchema = commonPatch(camsSchema, { pushPullOpts: [], pickedForSet: camsQueryProperties })
export type CamsPatch = Static<typeof camsPatchSchema>
export const camsPatchValidator = getValidator(camsPatchSchema, dataValidator)
export const camsPatchResolver = resolve<CamsPatch, HookContext>({})
export const camsQuerySchema = queryWrapper(camsQueryProperties, {
  name: Type.Any(),
  extras: Type.Object({
    $exists: Type.Boolean()
  }, { additionalProperties: true }),
  comp: Type.Object({
    $exists: Type.Boolean()
  }, { additionalProperties: true })
}, { opMap: { comp: ['$in'] } })
export type CamsQuery = Static<typeof camsQuerySchema>
export const camsQueryValidator = getValidator(camsQuerySchema, queryValidator)
export const camsQueryResolver = resolve<CamsQuery, HookContext>({})
