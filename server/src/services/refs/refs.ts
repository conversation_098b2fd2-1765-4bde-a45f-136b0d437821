// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import {hooks as schemaHooks} from '@feathersjs/schema'
import {scrubUploads} from '../../../utils/file-join.js';
import {relate} from '../../utils/relate/index.js';

const paths = ['avatar'];
const scrubUploadsConfig = {paths};
import {fileJoinHook} from '../../../utils/file-join.js';
import {logChange} from '../../../utils/change-log.js';
import {getJoin} from '../../../utils/fast-join.js';
import {errHook} from '../../../utils/simple.js';
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';

const authenticate = async (context: HookContext) => {
    const writer = [['refs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['refs', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };

    const cap_subjects:any = []
    if (['patch', 'remove'].includes(context.method)) {
        const ex = await loadExists(context);
        context = setExists(context, ex);
        const hostNamespace = `hosts:${ex.host}`
        cap_subjects.push(ex.host);
        const hostWrite: CapabilityParts[] = [[hostNamespace, 'hostAdmin'], [hostNamespace, 'refAdmin']]
        for (const w of hostWrite) {
            ucanArgs.patch.unshift(w);
        }
        if (ex.org) {
            cap_subjects.push(ex.org);
            const orgNamespace = `orgs:${ex.org}`
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin']];
            for (const w of orgWrite) {
                ucanArgs.patch.unshift(w);
            }
        }
    }

    return await allUcanAuth<HookContext>(ucanArgs, {
        creatorPass: ['*'],
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['person/owner'], '*']],
        cap_subjects
    })(context) as any;
}

const relatePpls = {
    herePath: 'person',
    therePath: 'refs',
    thereService: 'ppls',
    paramsName: 'relatePplsRefs'
};

const relateHost = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'host',
        therePath: 'refs',
        thereService: 'hosts',
        paramsName: 'relateRefHost'
    })(context)
}

const joinPerson = async context => {
    if (context.params.runJoin?.ref_person) {
        const coreOpts = { skipJoins: !!context.result.avatar }
        if (context.result.person) {
            context = await getJoin({
                service: 'ppls',
                herePath: 'person',
                coreOpts
            })(context);
        } else if (context.result.org) context = await getJoin({service: 'orgs', herePath: 'org', coreOpts})(context)
        if (!context.result.avatar) {
            const {person, org, files} = context.result._fastjoin || {}
            context.result.avatar = person?.avatar || org?.avatar;
            context.result._fastjoin = {...context.result._fastjoin,
                files: {
                    ...files,
                    avatar: files?.avatar ? files.avatar : person?._fastjoin?.files?.avatar || org?._fastjoin?.files?.org
                }
            }
        }
    }
    if (context.result?.isHost) {
        context = await getJoin({service: 'hosts', herePath: 'isHost'})(context)
    }
    return context;
};

const checkExistingPersonRefs = async (context: HookContext) => {
    const query:any = {person: context.data.person}
    if (context.data.host) query.host = context.data.host
    else query.host = {$exists: false}
    const ex = await new CoreCall('refs', context).find({ query })
    if(ex.total){
        context.data = {}
        context.result = ex.data[0]
    }
    return context;
}


import {
    refsDataValidator,
    refsPatchValidator,
    refsQueryValidator,
    refsResolver,
    refsExternalResolver,
    refsDataResolver,
    refsPatchResolver,
    refsQueryResolver
} from './refs.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {RefsService, getOptions} from './refs.class.js'
import {refsPath, refsMethods} from './refs.shared.js'

export * from './refs.class.js'
export * from './refs.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const refs = (app: Application) => {
    // Register our service on the Feathers application
    app.use(refsPath, new RefsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: refsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(refsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(refsExternalResolver),
                schemaHooks.resolveResult(refsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(refsQueryValidator),
                schemaHooks.resolveQuery(refsQueryResolver),
                scrubUploads(scrubUploadsConfig)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(refsDataValidator),
                schemaHooks.resolveData(refsDataResolver),
                checkExistingPersonRefs,
                relate('otm', relatePpls),
                relateHost
            ],
            patch: [
                schemaHooks.validateData(refsPatchValidator),
                schemaHooks.resolveData(refsPatchResolver),
                relate('otm', relatePpls),
                relateHost
            ],
            update: [
                relate('otm', relatePpls)
            ],
            remove: [relateHost]
        },
        after: {
            all: [scrubUploads(scrubUploadsConfig), fileJoinHook(paths)],
            find: [joinPerson],
            get: [joinPerson],
            create: [
                relate('otm', relatePpls),
                relateHost
            ],
            update: [
                relate('otm', relatePpls),
                relateHost
            ],
            patch: [
                relate('otm', relatePpls),
                relateHost
            ],
            remove: [
                relate('otm', relatePpls),
                relateHost
            ]
        },
        error: {
            all: [
                errHook()
            ]
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [refsPath]: RefsService
    }
}
