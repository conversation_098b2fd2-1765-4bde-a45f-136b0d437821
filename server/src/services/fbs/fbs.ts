// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    fbsDataValidator,
    fbsPatchValidator,
    fbsQueryValidator,
    fbsResolver,
    fbsExternalResolver,
    fbsDataResolver,
    fbsPatchResolver,
    fbsQueryResolver
} from './fbs.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {FbsService, getOptions} from './fbs.class.js'
import {fbsPath, fbsMethods} from './fbs.shared.js'
import {findJoin} from '../../utils/fast-join.js';
import {logChange} from '../../utils/change-log.js';
import {relate} from '../../utils/relate/index.js';
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, noThrow, setExists} from 'feathers-ucan';

export * from './fbs.class.js'
export * from './fbs.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['fbs', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['fbs', '*']] as Array<CapabilityParts>;
    const ucanArgs:any = {
        create: writer,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects: any = [];
    if (!['get', 'find'].includes(context.method)) {

        const existing = await loadExists(context, {});
        context = setExists(context, existing);
        const {org, host, updatedBy} = existing || {}
        const orgId = org || context.data.org;
        const hostId = host || context.data.org;
        //allow changes before approval
        if (orgId) {
            const orgNamespace = `orgs:${orgId}`;
            cap_subjects.push(orgId)
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'planAdmin']]
            for (const w of orgWrite) {
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
        } else ucanArgs.create = anyAuth
        if (hostId) {
            const hostNamespace = `hosts:${hostId}`;
            cap_subjects.push(hostId)
            const hostWrite: CapabilityParts[] = [[`orgs:${hostId}`, 'orgAdmin'], [hostNamespace, 'WRITE'], [hostNamespace, 'hostAdmin']]
            for (const w of hostWrite) {
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
        }
        if(!updatedBy?.login) ucanArgs.patch = noThrow;


    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        loginPass: [[['owner/owner'], '*'], [['canEdit/owner'], ['patch']]],
        or: '*',
        cap_subjects
    })(context) as any;
}

const runJoins = async (context: HookContext) => {
    const { with_responses, with_person } = context.params.runJoin || {}
    if(with_responses) return findJoin({
        herePath: 'responses',
        therePath: '_id',
        service: 'fb-res',
        params: { runJoin: { with_person }}
    })(context)
}

// const scrubNext = (context:HookContext) => {
//     if (['create', 'patch'].includes(context.method) && context.data.fields) {
//         for(let i = 0; i < context.data.fields.length; i++) {
//             if(context.data.fields[i].next) context.data.fields[i].next = context.data.fields[i].next.filter(a => !!a.field);
//         }
//     }
//     return context;
// }


const uniquePath = (context: HookContext) => {
    let fields = context.data?.fields;
    //console.log('fields', fields);
    if (fields && Array.isArray(fields)) {
        let names: any = [];
        for (let i = 0; i < fields.length; i++) {
            let field = fields[i];
            //console.log('field before', field.path);
            if (!names.includes(field.label)) {
                names.push(field.label);
            } else {
                field.label += String(i);
                names.push(field.label);
            }
            fields[i].path = field.label;
            //console.log('field after', field.path, fields[i].path);
        }
    }
    return context;
};

const rmvFalseIds = (context: HookContext) => {
    if (context.data.fields) {
        context.data.fields.forEach((field: any) => {
            if (field._id && field._id.indexOf('field') > -1) {
                delete field._id;
            }
        });
    }
    return context;
};

const relateParent = async (context: HookContext) => {
    const config = {
        herePath: 'parent',
        therePath: 'children',
        thereService: 'fbs',
        paramsName: 'relateParent'
    };

    return relate('otm', config)(context)
};

const setNext = async (context:HookContext) => {
    let { fields = [] } = context.data;
    if (fields.length) {
        for(let i = 0; i < fields.length; i++) {
            if (fields[i+1]) {
                const field = fields[i];
                let arr: any = [];
                if ((field.next || [])[0]) {
                    arr = field.next.filter(a => !!a.if);
                }
                let obj = {field: fields[i+1]?.id };
                arr ? arr.unshift(obj) : arr = [obj];
                field.next = arr;
            }
        }
    }
    return context;
};


// A configure function that registers the service and its hooks via `app.configure`
export const fbs = (app: Application) => {
    // Register our service on the Feathers application
    app.use(fbsPath, new FbsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: fbsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(fbsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(fbsExternalResolver),
                schemaHooks.resolveResult(fbsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(fbsQueryValidator),
                schemaHooks.resolveQuery(fbsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                setNext,
                schemaHooks.validateData(fbsDataValidator),
                schemaHooks.resolveData(fbsDataResolver),
                rmvFalseIds, uniquePath, relateParent
            ],
            update: [
                setNext,
                uniquePath,rmvFalseIds, relateParent
            ],
            patch: [
                setNext,
                schemaHooks.validateData(fbsPatchValidator),
                schemaHooks.resolveData(fbsPatchResolver),
                uniquePath,rmvFalseIds, relateParent
            ],
            remove: []
        },
        after: {
            all: [runJoins],
            find: [],
            get: [],
            create: [relateParent],
            update: [relateParent],
            patch: [relateParent],
            remove: [relateParent]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [fbsPath]: FbsService
    }
}
