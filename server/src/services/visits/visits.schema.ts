// TypeBox schema for visits service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, queryWrapper, commonPatch, imageSchema} from '../../utils/common/typebox-schemas.js'
import {exesSchema} from '../cares/schemas/index.js';

export const visitsSchema = Type.Object({
    _id: ObjectIdSchema(),
    provider: ObjectIdSchema(),
    patient: ObjectIdSchema(),
    person: Type.Optional(ObjectIdSchema()),
    care: ObjectIdSchema(),
    plan: ObjectIdSchema(),
    preventive: Type.Optional(Type.Boolean()),
    status: Type.Optional(Type.Union([
        Type.Literal("appointment"),
        Type.Literal("record"),
        Type.Literal("cancelled")
    ])),
    date: Type.Any(),
    endDate: Type.Optional(Type.Any()),
    category: Type.Optional(Type.Union([
        Type.Literal("emergency_room"),
        Type.Literal("primary_care"),
        Type.Literal("urgent_care"),
        Type.Literal("dental"),
        Type.Literal("specialist"),
        Type.Literal("mental"),
        Type.Literal("drug")
    ])),
    er: Type.Optional(Type.Boolean()),
    conditions: Type.Optional(Type.Array(exesSchema)),
    claims: Type.Optional(Type.Array(ObjectIdSchema())),
    claimReqs: Type.Optional(Type.Array(ObjectIdSchema())),
    total: Type.Optional(Type.Number()),
    subtotal: Type.Optional(Type.Number()),
   ...paidSchema.properties,
    balance: Type.Optional(Type.Number()),
    balanceSyncedAt: Type.Optional(Type.Any()),
    enteredBy: Type.Optional(Type.Object({
        id: Type.Optional(ObjectIdSchema()),
        org: Type.Optional(ObjectIdSchema()),
        auto: Type.Optional(Type.Boolean())
    })),
    practitioners: Type.Optional(Type.Array(ObjectIdSchema())),
    threads: Type.Optional(Type.Array(ObjectIdSchema())),
    files: Type.Optional(Type.Record(Type.String(), imageSchema)),
    ...commonFields.properties
}, {additionalProperties: false})

export type Visits = Static<typeof visitsSchema>
export const visitsValidator = getValidator(visitsSchema, dataValidator)
export const visitsResolver = resolve<Visits, HookContext>({
    date: async (val) => {
        if (!val) return new Date();
        return val
    },
    status: async (val, data) => {
        if (!val) {
            if (data.date) if (new Date(data.date as any).getTime() < new Date().getTime())
                return 'appointment';
            else return 'record'
        }
        return val;
    }

})
export const visitsExternalResolver = resolve<Visits, HookContext>({})

export const visitsDataSchema = Type.Object({
    ...Type.Omit(visitsSchema, ['_id']).properties
}, {additionalProperties: false})

export type VisitsData = Static<typeof visitsDataSchema>
export const visitsDataValidator = getValidator(visitsDataSchema, dataValidator)
export const visitsDataResolver = resolve<VisitsData, HookContext>({
    status: async (val, data) => {
        if (!val) {
            if (data.date) return 'record';
            else return 'appointment'
        }
        return val;
    }

})

// Pick ObjectId fields and nested ObjectId fields for query properties
const visitsQueryProperties = Type.Pick(visitsSchema, ['_id', 'provider', 'patient', 'person', 'care', 'plan', 'claims', 'claimReqs', 'practitioners', 'threads', 'createdBy', 'updatedBy', 'env', 'host', 'ref', 'changeLog'])

export const visitsPatchSchema = commonPatch(visitsSchema, {
    pushPullOpts: [
        {path: 'claims', type: ObjectIdSchema()},
        {path: 'practitioners', type: ObjectIdSchema()},
        {path: 'claimReqs', type: ObjectIdSchema()},
        {path: 'conditions', type: exesSchema }
    ],
    pickedForSet: visitsQueryProperties
})
export type VisitsPatch = Static<typeof visitsPatchSchema>
export const visitsPatchValidator = getValidator(visitsPatchSchema, dataValidator)
export const visitsPatchResolver = resolve<VisitsPatch, HookContext>({})

export const visitsQuerySchema = queryWrapper(visitsQueryProperties, {
  date: Type.Any(),
  endDate: Type.Any()
})
export type VisitsQuery = Static<typeof visitsQuerySchema>
export const visitsQueryValidator = getValidator(visitsQuerySchema, queryValidator)
export const visitsQueryResolver = resolve<VisitsQuery, HookContext>({})
