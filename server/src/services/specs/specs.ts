// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    specsDataValidator,
    specsPatchValidator,
    specsQueryValidator,
    specsResolver,
    specsExternalResolver,
    specsDataResolver,
    specsPatchResolver,
    specsQueryResolver
} from './specs.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {SpecsService, getOptions} from './specs.class.js'
import {specsPath, specsMethods} from './specs.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {_flatten, _pick} from 'symbol-ucan';
import {getJoin} from '../../../utils/fast-join.js';
import {logChange} from '../../../utils/change-log.js';

export * from './specs.class.js'
export * from './specs.schema.js'
const authenticate = async (context: HookContext) => {
    const writer = [['plans', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['plans', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    const cap_subjects:any = []
    if (!['get', 'find'].includes(context.method)) {
        let existing:any = {org: undefined};
        if (context.method !== 'create') {
            existing = await loadExists(context);
            context = setExists(context, existing);
            //allow changes before approval
            if(!existing.approvedAt) context.params.admin_pass = true;
        }
        const orgId = existing.org || context.data.org
        if(orgId) {
            cap_subjects.push(orgId);
            const orgNamespace = `orgs:${orgId}`;
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'planAdmin']]
            for (const w of orgWrite) {
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
        }

    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects,
        or: '*'
    })(context) as any;
}

const checkApproval = async (context:HookContext):Promise<HookContext> => {
    if(!context.params.approving_spec) {
        const existing = await loadExists(context);
        context = setExists(context, existing);
        if (existing.approvedAt) {
            const {status} = context.data.$set || context.data || { status: ''};
            if(status && ['approved', 'archived'].includes(status)){
                context.data = { $set: { status }}
            } else throw new Error(`This special election was already approved and cannot be edited`)
        } else if (context.data.status === 'approved' || context.data.$set?.status === 'approved') {
            context.data.approvedAt = new Date();
            context.data.approvedBy = context.params.login._id;
        }
    }
    return context;
}

const handleApproval = async(context:HookContext):Promise<HookContext> => {
    const creating = context.method === 'create';
    const existing = creating ? { ...context.result } : await loadExists(context);
    if(!creating) context = setExists(context, existing);
    if(context.result.approvedAt && !existing.approvedAt){
        if(context.result.specialEnrollment){
            const er = await new CoreCall('enrollments', context, { skipJoins: true }).get(context.result.enrollment);
            const { org, version, plan, person } = er;
            const vArr = version.split('_');
            const newVersion = `${vArr[0]}_${Number(vArr[1]) + 1}`
            const newEr = {
                org,
                plan,
                person,
                version: newVersion,
                planYear: newVersion.split('_')[0],
                spec: context.result._id,
                open: new Date(),
                close: new Date(new Date().getTime() * 1000 * 60 * 60 * 24 * 30),
                status: 'not_started'
            }
            await new CoreCall('enrollments', context, { skipJoins: true }).create(newEr, { admin_pass: true })
        } else {
            const patchObj: any = {};
            for (const k in context.result.changes || {}) {
                const {newVal} = context.result.changes[k];
                if (newVal || newVal === false || newVal === 0) {
                    if (!patchObj.$set) patchObj.$set = {};
                    patchObj.$set[k] = newVal;
                } else {
                    if (!patchObj.$unset) patchObj.$unset = {};
                    patchObj.$unset[k] = ''
                }
            }
            await new CoreCall('enrollments', context, {skipJoins: true}).patch(context.result.enrollment, patchObj, {special_change: []})
            context.result = await new CoreCall('specs', context).patch(context.id as any, {
                changedAt: new Date()}, {
                admin_pass: true,
                approving_spec: true
            })
        }
    }
    return context;
}

const joinPerson = async (context:HookContext):Promise<HookContext> => {
    if(context.params.runJoin?.spec_person){
        return getJoin({ service: 'enrollments',
            herePath: 'enrollment',
            joinPath: 'person',
            through: {
                'person': {
                    service: 'ppls'
                }
            }
        })(context)
    }
    return context;
}


const planYearCheck = async (context:HookContext):Promise<HookContext> => {
    if(!context.data.planYear && context.data.enrollment){
        const er = await new CoreCall('enrollments', context, { skipJoins: true }).get(context.data.enrollment as any);
        context.data.planYear = er.planYear;
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const specs = (app: Application) => {
    // Register our service on the Feathers application
    app.use(specsPath, new SpecsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: specsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(specsPath).hooks({
        around: {
            all: [schemaHooks.resolveExternal(specsExternalResolver), schemaHooks.resolveResult(specsResolver)]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(specsQueryValidator),
                schemaHooks.resolveQuery(specsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                planYearCheck,
                schemaHooks.validateData(specsDataValidator),
                schemaHooks.resolveData(specsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(specsPatchValidator),
                schemaHooks.resolveData(specsPatchResolver),
                checkApproval
            ],
            remove: []
        },
        after: {
            all: [
                joinPerson
            ],
            create: [handleApproval],
            patch: [
                handleApproval
            ]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [specsPath]: SpecsService
    }
}
