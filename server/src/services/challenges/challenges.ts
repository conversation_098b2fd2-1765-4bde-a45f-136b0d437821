// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    challengesDataValidator,
    challengesPatchValidator,
    challengesQueryValidator,
    challengesResolver,
    challengesExternalResolver,
    challengesDataResolver,
    challengesPatchResolver,
    challengesQueryResolver
} from './challenges.schema.js'

import type {Application} from '../../declarations.js'
import {ChallengesService, getOptions} from './challenges.class.js'
import {challengesPath, challengesMethods} from './challenges.shared.js'
import {logChange} from '../../../utils/change-log.js';

export * from './challenges.class.js'
export * from './challenges.schema.js'

// A configure function that registers the service and its hooks via `app.configure`
export const challenges = (app: Application) => {
    // Register our service on the Feathers application
    app.use(challengesPath, new ChallengesService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: challengesMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(challengesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(challengesExternalResolver),
                schemaHooks.resolveResult(challengesResolver)
            ]
        },
        before: {
            all: [
                schemaHooks.validateQuery(challengesQueryValidator),
                schemaHooks.resolveQuery(challengesQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                logChange(),
                schemaHooks.validateData(challengesDataValidator),
                schemaHooks.resolveData(challengesDataResolver)
            ],
            patch: [
                logChange(),
                schemaHooks.validateData(challengesPatchValidator),
                schemaHooks.resolveData(challengesPatchResolver)
            ],
            remove: [logChange()]
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [challengesPath]: ChallengesService
    }
}
