import {CoreCall} from 'feathers-ucan';
import {HookContext} from '../../../declarations.js';

export const genPingArgs = async (context: HookContext): Promise<Ping> => {
    const origin = context.app.get('origin');
    const config: PingConfig = {
        subjectPath: 'from',
        subjectNamePath: 'fromName',
        subjectServicePath: 'fromModel',
        recipientServicePath: 'toModel',
        recipientPath: 'to',
        message: 'New bill',
        action: 'pay',
        category: 'bills',
        priority: 2
    }
    if (context.type === 'after') {
        config.message = `New bill from ${context.result.fromName || context.result.fromEmail}`

        config.link = `${origin}/bill/${context.result._id}`
    }
    return {
        create: {
            '*': { config }
        }
    }
}
