// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    priceEstimatesDataValidator,
    priceEstimatesPatchValidator,
    priceEstimatesQueryValidator,
    priceEstimatesResolver,
    priceEstimatesExternalResolver,
    priceEstimatesDataResolver,
    priceEstimatesPatchResolver,
    priceEstimatesQueryResolver
} from './price-estimates.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {PriceEstimatesService, getOptions} from './price-estimates.class.js'
import {priceEstimatesPath, priceEstimatesMethods} from './price-estimates.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, noThrow} from 'feathers-ucan';
import {logChange} from '../../../utils/change-log.js';

export * from './price-estimates.class.js'
export * from './price-estimates.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['price-estimates', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['price-estimates', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: noThrow,
        patch: noThrow,
        update: writer,
        remove: deleter
    };

    const ctx = await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove']
    })(context)
        .catch(err => {
            if (context.method === 'create') return;
            throw new Error(err.message);
        })
    return ctx;
}

const checkLocationCode = (context: HookContext) => {
    if (!context.data.locationCode && !context.result) {
        context.data.locationCode = context.data.code;
    }
    return context;
}

import {aiPriceSearch} from './utils/ai.js';

const aiSearch = async (context: HookContext): Promise<HookContext> => {
    const {price_check} = context.params.runJoin || {};
    if (price_check) {
        const {rerun, searchList} = price_check;
        return await aiPriceSearch({rerun, searchList})(context);
    }
    return context;
}

import multer from 'multer';
import leven from 'leven';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    await new Promise((resolve) => multipartMiddleware.array('files', 5)(req, res, resolve));
    req.feathers.files = req.files;
    req.feathers.runJoin = req.query.runJoin;
    req.feathers.core = req.query.core
    return next();
}

const limitAlts = async (context: HookContext) => {
    const {alts} = context.result || {}
    if (alts?.length) {
        const getAmtString = (val) => `${val.medicare}|${val.cash}`
        const strings: Array<string> = [];
        const keep: any = [];
        for (let i = alts.length - 1; i > -1; i--) {
            const str = getAmtString(alts[i]);
            if (strings.includes(str)) alts.splice(i, 1);
            else {
                strings.push(str);
                const {medicare, cash} = alts[i];

                // Find the correct insertion point using findIndex
                const index = keep.findIndex((item: any) => item.cash > cash || (!item.cash && item.medicare > medicare));
                if (index === -1) {
                    // If no smaller distance is found, add to the end
                    keep.push(alts[i]);
                } else {
                    // Insert at the found index
                    keep.splice(index, 0, alts[i]);
                }

            }
        }
        new CoreCall('price-estimates', context).patch(context.result._id, {alts: keep.slice(0, 50)})
            .catch(err => console.log(`Error patching alts: ${err.message}`))
        context.result.alts = keep;
    }
    return context;
}
import {medicareCheck} from './utils/medicare.js';
import { getPtFile } from './utils/ptf.js'

// A configure function that registers the service and its hooks via `app.configure`
export const priceEstimates = (app: Application) => {
    // Register our service on the Feathers application
    app.use(priceEstimatesPath,
        restMiddleware,
        new PriceEstimatesService(getOptions(app)), {
            // A list of all methods this service exposes externally
            methods: priceEstimatesMethods,
            // You can add additional custom events to be sent to clients here
            events: []
        })
    // Initialize hooks
    app.service(priceEstimatesPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(priceEstimatesExternalResolver),
                schemaHooks.resolveResult(priceEstimatesResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(priceEstimatesQueryValidator),
                schemaHooks.resolveQuery(priceEstimatesQueryResolver)
            ],
            find: [aiSearch, getPtFile],
            get: [medicareCheck],
            create: [
                checkLocationCode,
                schemaHooks.validateData(priceEstimatesDataValidator),
                schemaHooks.resolveData(priceEstimatesDataResolver)

            ],
            patch: [
                schemaHooks.validateData(priceEstimatesPatchValidator),
                schemaHooks.resolveData(priceEstimatesPatchResolver)
            ],
            remove: []
        },
        after: {
            all: [],
            find: [],
            patch: [limitAlts]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [priceEstimatesPath]: any
    }
}
