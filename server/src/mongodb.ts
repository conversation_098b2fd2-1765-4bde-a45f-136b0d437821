// For more information about this file see https://dove.feathersjs.com/guides/cli/databases.html
import {MongoClient} from 'mongodb';
import type {Db} from 'mongodb';
import type {Application} from './declarations.js';

declare module './declarations.js' {
    interface Configuration {
        mongodbClient: Promise<Db>;
    }
}

export const mongodb = (app: Application) => {
    const connection = app.get('mongodb') as string;
    const database = new URL(connection).pathname.substring(1);

    const mongoClient = MongoClient.connect(connection, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        maxIdleTimeMS: 30000,
        retryWrites: true,
        retryReads: true
    }).then((client) => {
        console.log('MongoDB connected successfully');
        app.set('mongoClient', client);

        // Handle connection events
        client.on('error', (error) => {
            console.error('MongoDB connection error:', error);
        });

        client.on('close', () => {
            console.warn('MongoDB connection closed');
        });

        client.on('reconnect', () => {
            console.log('MongoDB reconnected');
        });

        return client.db(database);
    }).catch((error) => {
        console.error('Failed to connect to MongoDB:', error);
        console.error('Application will continue but database operations will fail');

        // Create a mock database object that throws errors for operations
        // This allows the app to start but makes it clear when DB operations fail
        const mockDb = {
            collection: () => {
                throw new Error('MongoDB connection failed - database unavailable');
            },
            admin: () => ({
                ping: () => Promise.reject(new Error('MongoDB connection failed'))
            }),
            command: () => Promise.reject(new Error('MongoDB connection failed'))
        } as any;

        return mockDb as Db;
    });

    app.set('mongodbClient', mongoClient);
};
