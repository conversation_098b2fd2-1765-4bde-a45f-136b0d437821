import { LocalStrategy } from '@feathersjs/authentication-local';
import { NotAuthenticated } from '@feathersjs/errors';
import {_unset} from '../../utils/dash-utils.js';

export class LocalAuth extends LocalStrategy {

  async findEntity(username, params) {
    const { usernameField } = params.loginOptions || {};
    params.skip_hooks = true;
    params.admin_pass = true;
    // console.log('find entity', username, usernameField);
    if (!username) { // don't query for users without any condition set.
      throw new NotAuthenticated(`No ${usernameField} passed to local authentication`);
    }

    const query = await this.getEntityQuery({
      [usernameField]: username
    }, params);

    const findParams = Object.assign({}, params, { query });
    const entityService = this.entityService;

    const result = await entityService.find({...findParams, skipJoins: true});
    const list = Array.isArray(result) ? result : result.data;

    if (!Array.isArray(list) || list.length === 0) {
      throw new NotAuthenticated(`No login found for ${usernameField} ${username}`);
    }

    const [ entity ] = list;

    return entity;

  }

  async authenticate (data, params) {
    const { passwordField, entity } = this.configuration;
    const { usernameField } = params.loginOptions || data.email ? { usernameField: 'email' } : { usernameField: 'phone' };
    params.loginOptions ? params.loginOptions.usernameField = usernameField : params.loginOptions = { usernameField };
    const username = data[usernameField];
    const password = data[passwordField];
    const result = await this.findEntity(username, _unset(params, 'provider'));

    await this.comparePassword(result, password);

    return {
      authentication: { strategy: this.name as string },
      [entity]: await this.getEntity(result, params)
    };
  }
}

