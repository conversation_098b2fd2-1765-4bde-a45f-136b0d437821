import {HookContext} from '../../../declarations.js';

export const allowApiKey = async (context:HookContext) => {
    const { params, app } = context;
    const config:any = app.get('authentication')?.api_config;
    const api_key = (params.headers || {})[config.headers.key];
    const client_id = (params.headers || {})[config.headers.id];
    if(api_key && client_id && params.provider && !params.authentication){
        context.params = {
            ...params,
            authentication: {
                strategy: 'apiKey',
                api_key,
                client_id
            }
        }
    }
    return context;
}
