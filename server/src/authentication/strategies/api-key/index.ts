import {
    AuthenticationBaseStrategy
} from '@feathersjs/authentication';
import type {AuthenticationResult} from '@feathersjs/authentication';
import {NotAuthenticated} from '@feathersjs/errors';
import { Application } from '../../../declarations.js'


export class ApiKeyStrategy extends AuthenticationBaseStrategy {
    app: Application;

    constructor(app: Application) {
        super();
        this.app = app;
    }

    async authenticate(authentication: AuthenticationResult, params:any) {
        const { api_key, client_id } = authentication;
        if(!api_key || !client_id) throw new NotAuthenticated('No api_key or no client_id passed');

        const config:any = this.app.get('authentication')?.api_config;
        const key = config.allowed_keys[client_id];

        const match = key === api_key;
        if (!match) throw new NotAuthenticated('Incorrect API Key');

        return {
            apiKey: true
        }
    }
}
