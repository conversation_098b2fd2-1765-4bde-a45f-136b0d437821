import {OAuthStrategy} from '@feathersjs/authentication-oauth';
import {handleRedirect} from './utils.js';
import { _get } from '../../../utils/dash-utils.js';

import axios from 'axios';
import {Application} from '../../../declarations.js';

export class FacebookStrategy extends OAuthStrategy {
    app?: Application = undefined

    constructor(app) {
        super();
        this.app = app;
    }
    async getRedirect(data, params) {
        return handleRedirect(data, params);
    }

    async getProfile(authResult) {
        // This is the OAuth access token that can be used
        // for Facebook API requests as the Bearer token
        const accessToken = authResult.access_token;

        const { data } = await axios.get('https://graph.facebook.com/me', {
            headers: {
                authorization: `Bearer ${accessToken}`
            },
            params: {
                // There are
                fields: 'id,name,email,picture'
            }
        });

        return data;
    }

    async getEntityData(profile) {
        // `profile` is the data returned by getProfile
        const baseData = await super.getEntityData(profile, null, {});

        return {
            ...baseData,
            avatar: { large: { file: _get(profile, 'picture') } },
            name: profile.name,
            email: profile.email
        };
    }
    async findEntity(profile) {
        const existing = await this.app?.service('logins').find({
            query: { email: profile.email }
        });
        return _get(existing, 'data[0]');
    }

    async updateEntity(entity, profile) {
        let change = false;
        if (!entity.isVerified) {
            change = true;
            entity.isVerified = true;
        }
        if (!entity.email) entity.email = profile.email;
        if (!entity.name) entity.name = profile.name;
        if (!_get(entity, 'avatar.large.file') && profile.picture) {
            entity.avatar = { large: { file: profile.picture } };
        }
        if (change) {
            return await this.app?.service('logins').patch(entity._id, entity);
        } else return entity;
    }
}
