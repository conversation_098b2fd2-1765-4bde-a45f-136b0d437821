import {OAuthStrategy} from '@feathersjs/authentication-oauth';
import {handleRedirect} from '../../../authentication/strategies/oauth/utils.js';
import axios from 'axios';
import {Application, HookContext} from '../../../declarations.js';
import {_get} from '../../../utils/dash-utils.js';

import {AuthenticationParams, AuthenticationRequest} from '@feathersjs/authentication';

export class LinkedinStrategy extends OAuthStrategy {
    app?: Application = undefined

    constructor(app) {
        super();
        this.app = app;
    }

    async getRedirect(data, params) {
        return handleRedirect(data, params);
    }

    async getProfile(authResult, params?: any) {
        // This is the OAuth access token that can be used
        console.log(authResult);
        const client_id = _get(this.app?.get('authentication'), 'oauth.linkedin.key');
        const secret = _get(this.app?.get('authentication'), 'oauth.linkedin.secret')

        const profile = await axios.get('https://api.linkedin.com/v2/userinfo', {
            headers: {
                Authorization: `Bearer ${authResult.access_token}`,
            }
        })
            .catch(err => {
                console.log('linkedin get profile error', err);
            });

        const {name, picture, email, sub} = profile['data'] as any;

        return {
            sub,
            name,
            avatar: {url: picture},
            email
        };
    }

    async getEntityData(profile) {
        // `profile` is the data returned by getProfile
        const baseData = await super.getEntityData(profile, null, {});

        return {
            ...baseData,
            avatar: profile.avatar,
            name: profile.name,
            email: profile.email
        };
    }

    async findEntity(profile, params?: any) {
        const existing = await this.app?.service('logins').find({
            query: {email: profile.email},
            admin_pass: true,
            skip_hooks: true
        });
        return _get(existing, 'data[0]');
    }

    async updateEntity(entity, profile, params) {
        let pChange = false;
        const changeEntity: typeof entity = {linkedinId: profile.sub}
        let person: {
            [key: string]: any
        } = {..._get(entity, '_fastjoin.owner')};
        if (!entity.isVerified) {
            changeEntity.isVerified = true;
        }
        if (!entity.email) {
            let email = entity.email;
            if(person?.email) email = person.email
            else {
                pChange = true;
                person.email = profile.email;
            }
            changeEntity.email = email;

        }
        if (!person?.name) {
            pChange = true;
            person.name = profile.name;
        }
        if (!_get(entity, 'avatar.url') && profile.avatar && !person.avatar) {
            pChange = true;
            person.avatar = profile.avatar;
        }
        if (pChange) {
            let call = 'create';
            const args = [person, { admin_pass: true }];
            if (person._id) {
                call = 'patch';
                args.unshift(person._id);
            } else delete person._id;
            const p = await this.app?.service('ppls')[call](...args)
                .catch(err => {
                    console.log(err.message);
                })
            if (!entity.owner) {
                changeEntity.owner = p?._id;
            }
        }
        const saved = await this.app?.service('logins').patch(entity._id, changeEntity, {...params, query: undefined, admin_pass: true });
        return saved || entity
    }


    //@ts-ignore
    async authenticate(authentication: AuthenticationRequest, originalParams: AuthenticationParams) {
        const entity: string = this.configuration.entity
        const {provider, ...params} = originalParams
        const profile = await this.getProfile(authentication, params)
            .catch(err => {
                throw new Error(err.message)
            })
        const existingEntity = (await this.findEntity(profile, params)) || (await this.getCurrentEntity(params))

        const authEntity = !existingEntity
            ? await this.createEntity(profile, params)
            : await this.updateEntity(existingEntity, profile, params)

        return {
            authentication: {strategy: this.name},
            [entity]: await this.getEntity(authEntity, originalParams)
        }
    }
}
