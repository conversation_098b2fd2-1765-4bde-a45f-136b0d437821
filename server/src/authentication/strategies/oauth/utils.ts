import { _get } from '../../../utils/dash-utils.js';

export const parseCookie = (cookie, name) => {
    let one = cookie.split(`${name}=`)[1];
    return one ? one.split(';')[0] : '';
};

// const decryptKey = process.env.TRANSPORT_SECRET;

export const oAuthDomainConfig = (params) => {
    // let cookie = parseCookie(params.headers.cookie, 'hostDomain');
    // if (cookie) domain = cookie.split(';')[0] || process.env.AUTH_OAUTH_REDIRECT;
    // if (domain) params.redirect = crypt.decryptSymmetric(domain, decryptKey);
    let domain = _get(params, 'query.redirect') || params.core?.ltail;
    if (domain) params.redirect = domain;
    return params.redirect;
};

export const handleRedirect = (data, params) => {

    let beforeToken = params.query.redirect || 'https://commoncare.org/oauth';
    // let beforeToken = 'http://localhost:8080/client#'; //test line for localhost
    return `${beforeToken}?access_token=${data.accessToken}`;
};
