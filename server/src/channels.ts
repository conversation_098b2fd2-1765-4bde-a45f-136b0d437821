// For more information about this file see https://dove.feathersjs.com/guides/cli/channels.html
import type {RealTimeConnection, Params} from '@feathersjs/feathers';
import type {AuthenticationResult} from '@feathersjs/authentication';
import '@feathersjs/transport-commons';
import type {Application, HookContext} from './declarations.js';
import {logger} from './logger.js';

export const channels = (app: Application) => {
    logger.info('Configuring Socket.IO channels...');

    // Track active connections for monitoring
    let connectionCount = 0;
    const connectionsByUser = new Map<string, Set<RealTimeConnection>>();

    app.on('connection', (connection: RealTimeConnection) => {
        connectionCount++;
        console.log(`[Channels] New connection (total: ${connectionCount})`);

        // On a new real-time connection, add it to the anonymous channel
        app.channel('anonymous').join(connection);

        // Clean up on disconnect - with proper error handling
        if (typeof connection.on === 'function') {
            connection.on('disconnect', () => {
                connectionCount--;
                console.log(`[Channels] Connection closed (total: ${connectionCount})`);

                // Clean up user connection tracking
                for (const [userId, connections] of connectionsByUser.entries()) {
                    connections.delete(connection);
                    if (connections.size === 0) {
                        connectionsByUser.delete(userId);
                    }
                }
            });
        }
    });

    // app.service('shops').publish((shop) => {
    //
    // })
    app.service('shops').on('patched', (shop, { params }) => {
        if (shop._id && params.connection) {
            app.channel(`shops/${shop._id}`).join(params.connection)
        }
    })
    app.service('shops').on('created', (shop, { params }) => {
        if (shop._id && params.connection) {
            app.channel(`shops/${shop._id}`).join(params.connection)
        }
    })
    app.service('ims').on('patched', (im, { params }) => {
        if (im._id && params.connection) {
            app.channel(`ims/${im._id}`).join(params.connection)
        }
    })
    app.service('ims').on('created', (im, { params }) => {
        if (im._id && params.connection) {
            app.channel(`ims/${im._id}`).join(params.connection)
        }
    })


    app.on(
        'login',
        (authResult: AuthenticationResult, {connection}: Params) => {
            // connection can be undefined if there is no
            // real-time connection, e.g. when logging in via REST
            if (connection) {
                const userId = authResult.user?._id || authResult.user?.id;

                // Track user connections
                if (userId) {
                    if (!connectionsByUser.has(userId)) {
                        connectionsByUser.set(userId, new Set());
                    }
                    connectionsByUser.get(userId)!.add(connection);

                    // Limit connections per user (prevent abuse)
                    const userConnections = connectionsByUser.get(userId)!;
                    if (userConnections.size > 5) {
                        console.warn(`[Channels] User ${userId} has ${userConnections.size} connections`);
                    }
                }

                // The connection is no longer anonymous, remove it
                app.channel('anonymous').leave(connection);

                // Add it to the authenticated user channel
                app.channel('authenticated').join(connection);

                // Add to user-specific channel
                if (userId) {
                    app.channel(`user:${userId}`).join(connection);
                }
            }
        }
    );

    // Configure service-specific publishers
    app.service('shops').publish('patched', (data: any) => {
        console.log(`[Channels] Publishing shop patched: ${data._id}`);
        if(data._id) return app.channel(`shops/${data._id}`);
        return [];
    });

    app.service('ims').publish('patched', (data: any) => {
        console.log(`[Channels] Publishing im patched: ${data._id}`);
        if(data._id) return app.channel(`ims/${data._id}`);
        return [];
    });

    // Default publisher for all other events
    app.publish((data: any, context: HookContext) => {
        console.log(`[Channels] Publishing ${context.service}:${context.method} to authenticated channel`);
        console.log(`[Channels] Connection authenticated:`, !!context.params?.connection);
        console.log(`[Channels] Authenticated channel size:`, app.channel('authenticated').length);
        console.log(`[Channels] Anonymous channel size:`, app.channel('anonymous').length);

        // For unauthenticated requests, publish to anonymous channel
        if (!context.params?.user) {
            console.log(`[Channels] Publishing to anonymous channel (no user)`);
            return app.channel('anonymous');
        }

        // Publish all service events to authenticated users by default
        return app.channel('authenticated');
    });
};
