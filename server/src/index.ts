console.log('[STARTUP] Starting server...');
console.log('[STARTUP] Node version:', process.version);
console.log('[STARTUP] Platform:', process.platform);
console.log('[STARTUP] Architecture:', process.arch);

// Environment variables should be available
console.log('[STARTUP] Environment variables check...');

// Catch any errors during import
try {
	console.log('[STARTUP] Importing app and logger...');
	var { app } = await import('./app.js');
	console.log('[STARTUP] App imported successfully');
	var { logger } = await import('./logger.js');
	console.log('[STARTUP] Logger imported successfully');
	console.log('[STARTUP] All imports successful');
} catch (error) {
	console.error('[STARTUP ERROR] Failed to import app or logger:', error);
	console.error('[STARTUP ERROR] Stack trace:', error instanceof Error ? error.stack : String(error));
	process.exit(1);
};
import { stopWatchdog } from './utils/watchdog/index.js';
import { resourceMonitor } from './utils/resource-monitor.js';
import { cleanupRedisAdapter } from './utils/socketio-redis.js';
import { stopSocketIOMonitoring } from './utils/socketio-monitor.js';

// Use config values - DigitalOcean will override via environment variables
const port = Number(process.env.PORT || app.get('port') || 3030);
// For containerized deployments, always bind to 0.0.0.0 for external access
// but keep the configured host for internal service discovery
const configuredHost = app.get('host');
const bindHost = process.env.NODE_ENV === 'production' ? '0.0.0.0' : (configuredHost || '0.0.0.0');

console.log('[STARTUP] Raw config values - port:', port, 'configured host:', configuredHost);
console.log('[STARTUP] Bind values - port:', port, 'bind host:', bindHost);

// Always show startup configuration for debugging
console.log('[STARTUP] Environment PORT:', process.env.PORT);
console.log('[STARTUP] Environment HOSTNAME:', process.env.HOSTNAME);
console.log('[STARTUP] Environment NODE_ENV:', process.env.NODE_ENV);
console.log('[STARTUP] Environment DATABASE_URL:', process.env.DATABASE_URL ? 'SET' : 'NOT SET');
console.log('[STARTUP] Environment VALKEY_URL:', process.env.VALKEY_URL ? 'SET' : 'NOT SET');
console.log('[STARTUP] App config port:', app.get('port'));
console.log('[STARTUP] App config host:', app.get('host'));
console.log('[STARTUP] App config mongodb:', app.get('mongodb') ? 'SET' : 'NOT SET');
console.log('[STARTUP] App config valkey url:', app.get('sessions')?.valkey?.url ? 'SET' : 'NOT SET');
console.log('[STARTUP] Final port:', port);
console.log('[STARTUP] Final bind host:', bindHost);
console.log('[STARTUP] Configured host (for services):', configuredHost);

let server: any;
let isShuttingDown = false;

// Enhanced error handling
process.on('unhandledRejection', (reason, p) => {
	logger.error('Unhandled Rejection at: Promise ', p, reason);
	console.error('[PROCESS] Unhandled Rejection at: Promise ', p, reason);
	// Don't exit immediately, let watchdog handle it
});

process.on('uncaughtException', (error) => {
	logger.error('Uncaught Exception:', error);
	console.error('[PROCESS] Uncaught Exception:', error);
	// Log the error but don't exit immediately
	// Let the watchdog detect the issue and handle graceful shutdown
});



// Add memory monitoring
process.on('warning', (warning) => {
	console.warn('[PROCESS] Warning:', warning.name, warning.message);
	logger.warn('Process warning:', warning);
});

// Graceful shutdown handling
const gracefulShutdown = async (signal: string) => {
	if (isShuttingDown) {
		logger.warn(`${signal} received again, forcing exit`);
		process.exit(1);
	}

	isShuttingDown = true;
	logger.info(`${signal} received, starting graceful shutdown...`);

	try {
		// Stop accepting new connections
		if (server) {
			await new Promise<void>((resolve, reject) => {
				server.close((err: any) => {
					if (err) reject(err);
					else resolve();
				});
			});
			logger.info('HTTP server closed');
		}

		// Stop Socket.IO monitoring and Redis connections
		stopSocketIOMonitoring();
		await cleanupRedisAdapter(app);

		// Stop watchdog and clean up resources
		stopWatchdog();
		resourceMonitor.cleanup();

		// Close MongoDB connections
		try {
			const mongoDb = await app.get('mongodbClient') as any;
			if (mongoDb && mongoDb.client) {
				await mongoDb.client.close();
				logger.info('MongoDB connection closed');
			}
		} catch (error) {
			logger.error('Error closing MongoDB connection:', error);
		}

		// Give remaining operations time to complete
		await new Promise(resolve => setTimeout(resolve, 5000));

		logger.info('Graceful shutdown completed');
		process.exit(0);
	} catch (error) {
		logger.error('Error during graceful shutdown:', error);
		process.exit(1);
	}
};

// Handle various shutdown signals (with error handling for containerized environments)
try {
	process.on('SIGTERM', () => {
		console.error('[PROCESS] Received SIGTERM - external termination request');
		logger.error('Received SIGTERM - external termination request');
		gracefulShutdown('SIGTERM');
	});
} catch (error:any) {
	console.warn('[PROCESS] Could not register SIGTERM handler:', error instanceof Error ? error.message : String(error));
}

try {
	process.on('SIGINT', () => {
		console.error('[PROCESS] Received SIGINT - interrupt signal');
		logger.error('Received SIGINT - interrupt signal');
		gracefulShutdown('SIGINT');
	});
} catch (error:any) {
	console.warn('[PROCESS] Could not register SIGINT handler:', error instanceof Error ? error.message : String(error));
}

try {
	process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart
} catch (error:any) {
	console.warn('[PROCESS] Could not register SIGUSR2 handler:', error instanceof Error ? error.message : String(error));
}

// Validate port and host before starting
if (!port) {
	console.error('[STARTUP ERROR] No port configured!');
	process.exit(1);
}

if (!bindHost) {
	console.error('[STARTUP ERROR] No bind host configured!');
	process.exit(1);
}

console.log(`[STARTUP] Attempting to start server on ${bindHost}:${port}`);

// Add more debugging
console.log('[STARTUP] Network interfaces available:');
try {
	const os = await import('os');
	const interfaces = os.networkInterfaces();
	Object.keys(interfaces).forEach(name => {
		const iface = interfaces[name];
		if (iface) {
			iface.forEach(details => {
				if (details.family === 'IPv4') {
					console.log(`[STARTUP]   ${name}: ${details.address}`);
				}
			});
		}
	});
} catch (err) {
	console.log('[STARTUP] Could not list network interfaces:', err);
}

server = app.listen(port, bindHost).then((httpServer: any) => {
	server = httpServer;
	console.log(`[STARTUP SUCCESS] Feathers app listening on http://${bindHost}:${port}`);
	console.log(`[STARTUP SUCCESS] Server address:`, httpServer.address());
	console.log(`[STARTUP SUCCESS] Configured host for services: ${configuredHost}`);
	logger.info(`Feathers app listening on http://${bindHost}:${port}`);

	// Test the health endpoint immediately
	setTimeout(async () => {
		try {
			const response = await fetch(`http://${bindHost === '0.0.0.0' ? 'localhost' : bindHost}:${port}/healthz`);
			console.log('[STARTUP] Health check response:', response.status, await response.text());
		} catch (error) {
			console.error('[STARTUP] Health check failed:', error);
		}
	}, 1000);

	return httpServer;
}).catch((error: any) => {
	console.error('[STARTUP ERROR] Failed to start server:', error);
	console.error('[STARTUP ERROR] Port:', port, 'Bind Host:', bindHost);
	console.error('[STARTUP ERROR] Error details:', error.message, error.code, error.errno);
	logger.error('Failed to start server:', error);
	process.exit(1);
});
