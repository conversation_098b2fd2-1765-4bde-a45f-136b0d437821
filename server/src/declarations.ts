// For more information about this file see https://dove.feathersjs.com/guides/cli/typescript.html
import { HookContext as FeathersHookContext, NextFunction } from '@feathersjs/feathers'
import { Application as FeathersApplication } from '@feathersjs/express'
import { ApplicationConfiguration } from './configuration.js'

export { NextFunction }

// The types for app.get(name) and app.set(name)
// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface Configuration extends ApplicationConfiguration {
    port:number,
    mongoClient: any,
    mailer: { key?:string },
    sms: { key?:string, id?:string, from?:string },
    origin: string,
    google: { api_key: string },
    salesTax: { api_key: string },
    banking: { moov: any, stripe: any },
    encryption: { field_key:string, transport_key:string, pin_key:string },
    openai: { key:string, org: string },
    turnstile: { turnstile_sk: string },
    sessions: { valkey: { url:string } }
}

// A mapping of service names to types. Will be extended in service files.
// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface ServiceTypes {}

// The application instance type that will be used everywhere else
export type Application = FeathersApplication<ServiceTypes, Configuration>

// The context for hook functions - can be typed with a service class
export type HookContext<S = any> = FeathersHookContext<Application, S>
