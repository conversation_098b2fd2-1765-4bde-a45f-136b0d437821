// For more information about this file see https://dove.feathersjs.com/guides/cli/client.html
import { feathers } from '@feathersjs/feathers'
import type { TransportConnection, Application } from '@feathersjs/feathers'
import authenticationClient from '@feathersjs/authentication-client'
import type { AuthenticationClientOptions } from '@feathersjs/authentication-client'

import { grpMbrsClient } from './services/grp-mbrs/grp-mbrs.shared.js'
export type { GrpMbrs, GrpMbrsData, GrpMbrsQuery, GrpMbrsPatch } from './services/grp-mbrs/grp-mbrs.shared.js'


import { errsClient } from './services/errs/errs.shared.js'
export type { Errs, ErrsData, ErrsQuery, ErrsPatch } from './services/errs/errs.shared.js'

import { passkeysClient } from './services/passkeys/passkeys.shared.js'
export type {
  Passkeys,
  PasskeysData,
  PasskeysQuery,
  PasskeysPatch
} from './services/passkeys/passkeys.shared.js'

import { challengesClient } from './services/challenges/challenges.shared.js'
export type {
  Challenges,
  ChallengesData,
  ChallengesQuery,
  ChallengesPatch
} from './services/challenges/challenges.shared.js'

import { expensesClient } from './services/expenses/expenses.shared.js'
export type {
  Expenses,
  ExpensesData,
  ExpensesQuery,
  ExpensesPatch
} from './services/expenses/expenses.shared.js'

import { healthSharesClient } from './services/health-shares/health-shares.shared.js'
export type {
  HealthShares,
  HealthSharesData,
  HealthSharesQuery,
  HealthSharesPatch
} from './services/health-shares/health-shares.shared.js'

import { aiChatsClient } from './services/ai-chats/ai-chats.shared.js'
export type { AiChats, AiChatsData, AiChatsQuery, AiChatsPatch } from './services/ai-chats/ai-chats.shared.js'


import { gpsClient } from './services/gps/gps.shared.js'
export type { Gps, GpsData, GpsQuery, GpsPatch } from './services/gps/gps.shared.js'

import { fbResClient } from './services/fb-res/fb-res.shared.js'
export type { FbRes, FbResData, FbResQuery, FbResPatch } from './services/fb-res/fb-res.shared.js'

import { fbsClient } from './services/fbs/fbs.shared.js'
export type { Fbs, FbsData, FbsQuery, FbsPatch } from './services/fbs/fbs.shared.js'

import { sePlansClient } from './services/se-plans/se-plans.shared.js'
export type { SePlans, SePlansData, SePlansQuery, SePlansPatch } from './services/se-plans/se-plans.shared.js'

import { walletsClient } from './services/wallets/wallets.shared'
export type { Wallets, WalletsData, WalletsQuery, WalletsPatch } from './services/wallets/wallets.shared.js'

import { fundsClient } from './services/funds/funds.shared.js'
export type { Funds, FundsData, FundsQuery, FundsPatch } from './services/funds/funds.shared.js'

import { mbrsClient } from './services/mbrs/mbrs.shared.js'
export type { Mbrs, MbrsData, MbrsQuery, MbrsPatch } from './services/mbrs/mbrs.shared.js'

import { teamsClient } from './services/teams/teams.shared.js'
export type { Teams, TeamsData, TeamsQuery, TeamsPatch } from './services/teams/teams.shared.js'

import { calendarsClient } from './services/calendars/calendars.shared.js'
export type {
  Calendars,
  CalendarsData,
  CalendarsQuery,
  CalendarsPatch
} from './services/calendars/calendars.shared.js'

import { imsClient } from './services/ims/ims.shared.js'
export type { Ims, ImsData, ImsQuery, ImsPatch } from './services/ims/ims.shared.js'

import { shopsClient } from './services/shops/shops.shared.js'
export type { Shops, ShopsData, ShopsQuery, ShopsPatch } from './services/shops/shops.shared.js'

import { billErasersClient } from './services/bill-erasers/bill-erasers.shared.js'
export type {
  BillErasers,
  BillErasersData,
  BillErasersQuery,
  BillErasersPatch
} from './services/bill-erasers/bill-erasers.shared.js'

import { priceEstimatesClient } from './services/price-estimates/price-estimates.shared.js'
export type {
  PriceEstimates,
  PriceEstimatesData,
  PriceEstimatesQuery,
  PriceEstimatesPatch
} from './services/price-estimates/price-estimates.shared.js'

import { offersClient } from './services/offers/offers.shared.js'
export type { Offers, OffersData, OffersQuery, OffersPatch } from './services/offers/offers.shared.js'

import { contractsClient } from './services/contracts/contracts.shared.js'
export type {
  Contracts,
  ContractsData,
  ContractsQuery,
  ContractsPatch
} from './services/contracts/contracts.shared.js'

import { cobrasClient } from './services/cobras/cobras.shared.js'
export type { Cobras, CobrasData, CobrasQuery, CobrasPatch } from './services/cobras/cobras.shared.js'

import { dropsClient } from './services/drops/drops.shared.js'
export type { Drops, DropsData, DropsQuery, DropsPatch } from './services/drops/drops.shared.js'

import { networksClient } from './services/networks/networks.shared.js'
export type {
  Networks,
  NetworksData,
  NetworksQuery,
  NetworksPatch
} from './services/networks/networks.shared.js'

import { capsClient } from './services/caps/caps.shared.js'
export type { Caps, CapsData, CapsQuery, CapsPatch } from './services/caps/caps.shared.js'

import { bundlesClient } from './services/bundles/bundles.shared.js'
export type { Bundles, BundlesData, BundlesQuery, BundlesPatch } from './services/bundles/bundles.shared.js'

import { claimReqsClient } from './services/claim-reqs/claim-reqs.shared.js'
export type {
  ClaimReqs,
  ClaimReqsData,
  ClaimReqsQuery,
  ClaimReqsPatch
} from './services/claim-reqs/claim-reqs.shared.js'

import { fundsRequestsClient } from './services/funds-requests/funds-requests.shared.js'
export type {
  FundsRequests,
  FundsRequestsData,
  FundsRequestsQuery,
  FundsRequestsPatch
} from './services/funds-requests/funds-requests.shared.js'

import { careAccountsClient } from './services/care-accounts/care-accounts.shared.js'
export type {
  CareAccounts,
  CareAccountsData,
  CareAccountsQuery,
  CareAccountsPatch
} from './services/care-accounts/care-accounts.shared.js'

import { budgetsClient } from './services/budgets/budgets.shared.js'
export type { Budgets, BudgetsData, BudgetsQuery, BudgetsPatch } from './services/budgets/budgets.shared.js'

import { hostsClient } from './services/hosts/hosts.shared.js'
export type { Hosts, HostsData, HostsQuery, HostsPatch } from './services/hosts/hosts.shared.js'

import { marketsClient } from './services/markets/markets.shared.js'
export type { Markets, MarketsData, MarketsQuery, MarketsPatch } from './services/markets/markets.shared.js'

import { ratesClient } from './services/rates/rates.shared.js'
export type { Rates, RatesData, RatesQuery, RatesPatch } from './services/rates/rates.shared.js'

import { bankingClient } from './services/banking/banking.shared.js'
export type { Banking, BankingData, BankingQuery, BankingPatch } from './services/banking/banking.shared.js'

import { ledgersClient } from './services/ledgers/ledgers.shared.js'
export type { Ledgers, LedgersData, LedgersQuery, LedgersPatch } from './services/ledgers/ledgers.shared.js'

import { crossSectionsClient } from './services/cross-sections/cross-sections.shared.js'
export type {
  CrossSections,
  CrossSectionsData,
  CrossSectionsQuery,
  CrossSectionsPatch
} from './services/cross-sections/cross-sections.shared.js'

import { bankAccountsClient } from './services/bank-accounts/bank-accounts.shared.js'
export type {
  BankAccounts,
  BankAccountsData,
  BankAccountsQuery,
  BankAccountsPatch
} from './services/bank-accounts/bank-accounts.shared.js'

import { claimsClient } from './services/claims/claims.shared.js'
export type { Claims, ClaimsData, ClaimsQuery, ClaimsPatch } from './services/claims/claims.shared.js'

import { salesTaxesClient } from './services/sales-taxes/sales-taxes.shared.js'
export type {
  SalesTaxes,
  SalesTaxesData,
  SalesTaxesQuery,
  SalesTaxesPatch
} from './services/sales-taxes/sales-taxes.shared.js'

import { pricesClient } from './services/prices/prices.shared.js'
export type { Prices, PricesData, PricesQuery, PricesPatch } from './services/prices/prices.shared.js'

import { claimPaymentsClient } from './services/claim-payments/claim-payments.shared.js'
export type {
  ClaimPayments,
  ClaimPaymentsData,
  ClaimPaymentsQuery,
  ClaimPaymentsPatch
} from './services/claim-payments/claim-payments.shared.js'

import { billsClient } from './services/bills/bills.shared.js'
export type { Bills, BillsData, BillsQuery, BillsPatch } from './services/bills/bills.shared.js'

import { visitsClient } from './services/visits/visits.shared.js'
export type { Visits, VisitsData, VisitsQuery, VisitsPatch } from './services/visits/visits.shared.js'

import { practitionersClient } from './services/practitioners/practitioners.shared.js'
export type {
  Practitioners,
  PractitionersData,
  PractitionersQuery,
  PractitionersPatch
} from './services/practitioners/practitioners.shared.js'

import { medsClient } from './services/meds/meds.shared.js'
export type { Meds, MedsData, MedsQuery, MedsPatch } from './services/meds/meds.shared.js'

import { conditionsClient } from './services/conditions/conditions.shared.js'
export type {
  Conditions,
  ConditionsData,
  ConditionsQuery,
  ConditionsPatch
} from './services/conditions/conditions.shared.js'

import { providersClient } from './services/providers/providers.shared.js'
export type {
  Providers,
  ProvidersData,
  ProvidersQuery,
  ProvidersPatch
} from './services/providers/providers.shared.js'

import { caresClient } from './services/cares/cares.shared.js'
export type { Cares, CaresData, CaresQuery, CaresPatch } from './services/cares/cares.shared.js'

import { specsClient } from './services/specs/specs.shared.js'
export type { Specs, SpecsData, SpecsQuery, SpecsPatch } from './services/specs/specs.shared.js'

import { docTemplatesClient } from './services/doc-templates/doc-templates.shared.js'
export type {
  DocTemplates,
  DocTemplatesData,
  DocTemplatesQuery,
  DocTemplatesPatch
} from './services/doc-templates/doc-templates.shared.js'

import { pingsClient } from './services/pings/pings.shared.js'
export type { Pings, PingsData, PingsQuery, PingsPatch } from './services/pings/pings.shared.js'

import { planDocsClient } from './services/plan-docs/plan-docs.shared.js'
export type {
  PlanDocs,
  PlanDocsData,
  PlanDocsQuery,
  PlanDocsPatch
} from './services/plan-docs/plan-docs.shared.js'

import { marketplaceClient } from './services/marketplace/marketplace.shared.js'
export type {
  Marketplace,
  MarketplaceData,
  MarketplaceQuery,
  MarketplacePatch
} from './services/marketplace/marketplace.shared.js'

import { householdsClient } from './services/households/households.shared.js'
export type {
  Households,
  HouseholdsData,
  HouseholdsQuery,
  HouseholdsPatch
} from './services/households/households.shared.js'

import { enrollmentsClient } from './services/enrollments/enrollments.shared.js'
export type {
  Enrollments,
  EnrollmentsData,
  EnrollmentsQuery,
  EnrollmentsPatch
} from './services/enrollments/enrollments.shared.js'

import { coveragesClient } from './services/coverages/coverages.shared.js'
export type {
  Coverages,
  CoveragesData,
  CoveragesQuery,
  CoveragesPatch
} from './services/coverages/coverages.shared.js'

import { proceduresClient } from './services/procedures/procedures.shared'
export type {
  Procedures,
  ProceduresData,
  ProceduresQuery,
  ProceduresPatch
} from './services/procedures/procedures.shared.js'

import { plansClient } from './services/plans/plans.shared'
export type { Plans, PlansData, PlansQuery, PlansPatch } from './services/plans/plans.shared.js'

import { camsClient } from './services/cams/cams.shared'
export type { Cams, CamsData, CamsQuery, CamsPatch } from './services/cams/cams.shared.js'

import { compsClient } from './services/comps/comps.shared.js'
export type { Comps, CompsData, CompsQuery, CompsPatch } from './services/comps/comps.shared.js'

import { cryptoClient } from './services/crypto/crypto.shared.js'
export type { Crypto, CryptoData, CryptoQuery, CryptoPatch } from './services/crypto/crypto.shared.js'

import { groupsClient } from './services/groups/groups.shared.js'
export type { Groups, GroupsData, GroupsQuery, GroupsPatch } from './services/groups/groups.shared.js'

import { junkDrawersClient } from './services/junk-drawers/junk-drawers.shared.js'
export type {
  JunkDrawers,
  JunkDrawersData,
  JunkDrawersQuery,
  JunkDrawersPatch
} from './services/junk-drawers/junk-drawers.shared.js'

import { issuesClient } from './services/issues/issues.shared.js'
export type { Issues, IssuesData, IssuesQuery, IssuesPatch } from './services/issues/issues.shared.js'

import { changeLogsClient } from './services/change-logs/change-logs.shared.js'
export type {
  ChangeLogs,
  ChangeLogsData,
  ChangeLogsQuery,
  ChangeLogsPatch
} from './services/change-logs/change-logs.shared.js'

import { uploadsClient } from './services/uploads/uploads.shared.js'
export type { Uploads, UploadsData, UploadsQuery, UploadsPatch } from './services/uploads/uploads.shared.js'

import { tomtomReverseGeocodeClient } from './services/tomtom-reverse-geocode/tomtom-reverse-geocode.shared.js'
export type {
  TomtomReverseGeocode,
  TomtomReverseGeocodeData,
  TomtomReverseGeocodeQuery,
  TomtomReverseGeocodePatch
} from './services/tomtom-reverse-geocode/tomtom-reverse-geocode.shared.js'

import { tomtomGeocodeClient } from './services/tomtom-geocode/tomtom-geocode.shared.js'
export type {
  TomtomGeocode,
  TomtomGeocodeData,
  TomtomGeocodeQuery,
  TomtomGeocodePatch
} from './services/tomtom-geocode/tomtom-geocode.shared.js'

import { threadsClient } from './services/threads/threads.shared.js'
export type { Threads, ThreadsData, ThreadsQuery, ThreadsPatch } from './services/threads/threads.shared.js'

import { reqsClient } from './services/reqs/reqs.shared.js'
export type { Reqs, ReqsData, ReqsQuery, ReqsPatch } from './services/reqs/reqs.shared.js'

import { docRequestsClient } from './services/doc-requests/doc-requests.shared.js'
export type {
  DocRequests,
  DocRequestsData,
  DocRequestsQuery,
  DocRequestsPatch
} from './services/doc-requests/doc-requests.shared.js'

import { refsClient } from './services/refs/refs.shared.js'
export type { Refs, RefsData, RefsQuery, RefsPatch } from './services/refs/refs.shared.js'

import { pplsClient } from './services/ppls/ppls.shared.js'
export type { Ppls, PplsData, PplsQuery, PplsPatch } from './services/ppls/ppls.shared.js'

import { orgsClient } from './services/orgs/orgs.shared.js'
export type { Orgs, OrgsData, OrgsQuery, OrgsPatch } from './services/orgs/orgs.shared.js'

import { myIpClient } from './services/my-ip/my-ip.shared.js'
export type { MyIp, MyIpData, MyIpQuery, MyIpPatch } from './services/my-ip/my-ip.shared.js'

import { loginsClient } from './services/logins/logins.shared.js'
export type { Logins, LoginsData, LoginsQuery, LoginsPatch } from './services/logins/logins.shared.js'

import { leadsClient } from './services/leads/leads.shared.js'
export type { Leads, LeadsData, LeadsQuery, LeadsPatch } from './services/leads/leads.shared.js'

export interface Configuration {
  connection: TransportConnection<ServiceTypes>
}

export interface ServiceTypes {}

export type ClientApplication = Application<ServiceTypes, Configuration>

/**
 * Returns a typed client for the server app.
 *
 * @param connection The REST or Socket.io Feathers client connection
 * @param authenticationOptions Additional settings for the authentication client
 * @see https://dove.feathersjs.com/api/client.html
 * @returns The Feathers client application
 */
export const createClient = <Configuration = any>(
  connection: TransportConnection<ServiceTypes>,
  authenticationOptions: Partial<AuthenticationClientOptions> = {}
) => {
  const client: ClientApplication = feathers()

  client.configure(connection)
  client.configure(authenticationClient(authenticationOptions))
  client.set('connection', connection)

  client.configure(leadsClient)
  client.configure(coveragesClient)
  client.configure(loginsClient)
  client.configure(myIpClient)
  client.configure(orgsClient)
  client.configure(pplsClient)
  client.configure(refsClient)
  client.configure(reqsClient)
  client.configure(docRequestsClient)
  client.configure(threadsClient)
  client.configure(tomtomGeocodeClient)
  client.configure(tomtomReverseGeocodeClient)
  client.configure(uploadsClient)
  client.configure(changeLogsClient)
  client.configure(issuesClient)
  client.configure(junkDrawersClient)
  client.configure(groupsClient)
  client.configure(cryptoClient)
  client.configure(compsClient)
  client.configure(camsClient)
  client.configure(plansClient)
  client.configure(proceduresClient)
  client.configure(enrollmentsClient)
  client.configure(householdsClient)
  client.configure(marketplaceClient)
  client.configure(planDocsClient)
  client.configure(docTemplatesClient)
  client.configure(pingsClient)
  client.configure(specsClient)
  client.configure(caresClient)
  client.configure(providersClient)
  client.configure(conditionsClient)
  client.configure(medsClient)
  client.configure(practitionersClient)
  client.configure(visitsClient)
  client.configure(billsClient)
  client.configure(claimPaymentsClient)
  client.configure(pricesClient)
  client.configure(salesTaxesClient)
  client.configure(claimsClient)
  client.configure(bankAccountsClient)
  client.configure(crossSectionsClient)
  client.configure(ledgersClient)
  client.configure(bankingClient)
  client.configure(ratesClient)
  client.configure(marketsClient)
  client.configure(hostsClient)
  client.configure(issuesClient)
  client.configure(budgetsClient)
  client.configure(careAccountsClient)
  client.configure(fundsRequestsClient)
  client.configure(claimReqsClient)
  client.configure(bundlesClient)
  client.configure(capsClient)
  client.configure(networksClient)
  client.configure(dropsClient)
  client.configure(cobrasClient)
  client.configure(contractsClient)
  client.configure(offersClient)
  client.configure(priceEstimatesClient)
  client.configure(billErasersClient)
  client.configure(shopsClient)
  client.configure(imsClient)
  client.configure(calendarsClient)
  client.configure(teamsClient)
  client.configure(mbrsClient)
  client.configure(fundsClient)
  client.configure(walletsClient)
  client.configure(sePlansClient)
  client.configure(fbsClient)
  client.configure(fbResClient)
  client.configure(compsClient)
  client.configure(gpsClient)
  client.configure(aiChatsClient)
  client.configure(healthSharesClient)
  client.configure(expensesClient)
  client.configure(challengesClient)
  client.configure(passkeysClient)
  client.configure(errsClient)
  client.configure(mbrsClient)
  client.configure(grpMbrsClient)
  return client
}
