{
  "ts-node": {
    "esm": true, // Enabling ESM for ts-node
    "files": true
  },
  "compilerOptions": {
    //    "noEmit": true,
    //    "allowImportingTsExtensions": true,
    "allowImportingTsExtensions": false, // optional — must match what your imports use
    "noImplicitAny": false,
    "moduleResolution": "node", // Improved resolution for ESM
    "target": "es2020",
    "module": "esNext",
    "outDir": "./lib",
    "rootDir": "./src",
    "declaration": true,
    "strict": true,
    "esModuleInterop": true,
    "sourceMap": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true // Helps with default import compatibility
  },
  "include": [
    "src/**/*.ts" // Simplified to match all TypeScript files in src
  ],
  "exclude": [
    "test",
    "node_modules"
  ]
}
