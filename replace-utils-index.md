## The file that was located at server/src/utils/index.ts is now removed. It was exporting * from several utility files in the server/src/utils folder. We need to find the files that are importing from that folder and change the imports to come from the correct individual utility files. 

#Step 1:

- Go to server/src/services and scan for 'from ../../utils.index.js', 'from ../../../utils.index.js', or from 'from ../../../../utils.index.js'
- Replace each import with the import from its utility file instead.
- Remove the old imports so we don't have duplicate imports

### There shouldn't be naming conflicts from any of the utils files, so you should definitively know where to import the function from, but if there are, keep a list of them and we'll go back and correctly place those


#Step 2: Test compile for import errors
