{"$id": "Hosts", "type": "object", "additionalProperties": false, "required": ["_id", "org", "dba"], "properties": {"_id": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "appDefault": {"type": "boolean"}, "dba": {"type": "string"}, "description": {"type": "string"}, "org": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "avatar": {"$comment": "***imageSchema used here***", "type": "object"}, "subdomain": {"type": "string"}, "allVideos": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "phones": {"type": "array", "items": {"type": "object", "additionalProperties": true, "properties": {"number": {"type": "object", "properties": {"input": {"type": "string"}, "international": {"type": "string"}, "national": {"type": "string"}, "e164": {"type": "string"}, "rfc3966": {"type": "string"}, "significant": {"type": "string"}}}, "regionCode": {"type": "string"}, "valid": {"type": "boolean"}, "possible": {"type": "boolean"}, "possibility": {"type": "string"}, "countryCode": {"type": "number"}, "canBeInternationallyDialled": {"type": "boolean"}, "typeIsMobile": {"type": "boolean"}, "typeIsFixedLine": {"type": "boolean"}}}}, "emails": {"type": "array", "items": {"type": "string"}}, "locations": {"$comment": "***addressSchema used here***", "type": "object"}, "refs": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "teams": {"type": "array", "items": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}, "npn": {"type": "string"}, "shopStatuses": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"label": {"type": "string"}, "color": {"type": "string"}}}}}, "publicSupport": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "plans": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"team": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}, "payContract": {"anyOf": [{"type": "string", "objectid": true}, {"type": "object", "properties": {}, "additionalProperties": true}]}}}}}, "videos": {"type": "object", "properties": {"intro": {"type": "object", "patternProperties": {"^.*$": {"$comment": "***imageSchema used here***", "type": "object"}}}}}, "states": {"type": "object", "patternProperties": {"[A-Z]{2}": {"type": "object", "properties": {"state": {"type": "string"}, "counties": {"type": "object", "patternProperties": {"^.*$": {"type": "object", "properties": {"all": {"type": "boolean"}, "cities": {"type": "array", "items": {"type": "string"}}}}}}, "all": {"type": "boolean"}}}}}, "roles": {"type": "array", "items": {"type": "string", "enum": ["care_director", "plan_guide", "compliance", "finance", "physician"]}}, "broker": {"type": "object", "properties": {"active": {"type": "boolean"}, "ichra": {"type": "boolean"}}}}}